{"ast": null, "code": "var _jsxFileName = \"D:\\\\HCCB\\\\Dashboard\\\\coca-cola-dashboard\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { <PERSON><PERSON>hart, Bar, XAxis, <PERSON>Axis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, PieChart, Pie, Cell, AreaChart, Area } from 'recharts';\nimport { HelpCircle, TrendingUp, DollarSign, CheckC<PERSON>cle, BarChart3, <PERSON><PERSON><PERSON> as Pie<PERSON><PERSON>, Layers3, <PERSON>lter, <PERSON><PERSON><PERSON><PERSON>, <PERSON>R<PERSON>, Lightbulb } from 'lucide-react';\n\n// --- Data extracted from the Annual Report (in thousands) ---\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst financialData = {\n  '2024': {\n    netSales: 6899716,\n    bottleCanNetSales: 6333316,\n    otherNetSales: 566400,\n    sparklingNetSales: 4106073,\n    stillNetSales: 2227243,\n    grossProfit: 2753179,\n    incomeFromOperations: 920350,\n    netIncome: 633125,\n    bottleCanSalesVolume: {\n      sparkling: 266686,\n      still: 86417,\n      total: 353103\n    },\n    sdeaExpenses: 1832829\n  },\n  '2023': {\n    netSales: 6653858,\n    bottleCanNetSales: 6041772,\n    otherNetSales: 612086,\n    sparklingNetSales: 3892133,\n    stillNetSales: 2149639,\n    grossProfit: 2598711,\n    incomeFromOperations: 834451,\n    netIncome: 408375,\n    bottleCanSalesVolume: {\n      sparkling: 263872,\n      still: 91495,\n      total: 355367\n    },\n    sdeaExpenses: 1764260\n  },\n  '2022': {\n    netSales: 6200957,\n    grossProfit: 2277954,\n    incomeFromOperations: 641047,\n    netIncome: 430158\n  }\n};\n\n// --- Chart Data Preparation ---\nconst historicalPerformance = [{\n  year: '2022',\n  'Net Sales': financialData['2022'].netSales / 1000,\n  'Gross Profit': financialData['2022'].grossProfit / 1000,\n  'Net Income': financialData['2022'].netIncome / 1000\n}, {\n  year: '2023',\n  'Net Sales': financialData['2023'].netSales / 1000,\n  'Gross Profit': financialData['2023'].grossProfit / 1000,\n  'Net Income': financialData['2023'].netIncome / 1000\n}, {\n  year: '2024',\n  'Net Sales': financialData['2024'].netSales / 1000,\n  'Gross Profit': financialData['2024'].grossProfit / 1000,\n  'Net Income': financialData['2024'].netIncome / 1000\n}];\nconst getSalesVolumeData = year => [{\n  name: 'Sparkling',\n  value: financialData[year].bottleCanSalesVolume.sparkling\n}, {\n  name: 'Still',\n  value: financialData[year].bottleCanSalesVolume.still\n}];\nconst netSalesBreakdown = [{\n  name: 'FY2023',\n  'Bottle/Can Sales': financialData['2023'].bottleCanNetSales,\n  'Other Sales': financialData['2023'].otherNetSales\n}, {\n  name: 'FY2024',\n  'Bottle/Can Sales': financialData['2024'].bottleCanNetSales,\n  'Other Sales': financialData['2024'].otherNetSales\n}];\nconst profitabilityData = [{\n  name: 'FY2023',\n  'Gross Profit': financialData['2023'].grossProfit,\n  'SD&A Expenses': financialData['2023'].sdeaExpenses,\n  'Income from Operations': financialData['2023'].incomeFromOperations\n}, {\n  name: 'FY2024',\n  'Gross Profit': financialData['2024'].grossProfit,\n  'SD&A Expenses': financialData['2024'].sdeaExpenses,\n  'Income from Operations': financialData['2024'].incomeFromOperations\n}];\nconst revenueCompositionData = [{\n  name: 'FY2023',\n  'Sparkling Sales': financialData['2023'].sparklingNetSales,\n  'Still Sales': financialData['2023'].stillNetSales\n}, {\n  name: 'FY2024',\n  'Sparkling Sales': financialData['2024'].sparklingNetSales,\n  'Still Sales': financialData['2024'].stillNetSales\n}];\nconst COLORS = ['#B91C1C', '#F87171'];\n\n// --- Revised Quiz Questions ---\nconst quizQuestions = [{\n  question: \"Which metric saw a higher percentage growth from FY2023 to FY2024?\",\n  options: [\"Net Sales\", \"Gross Profit\", \"Net Income\"],\n  correctAnswer: \"Net Income\",\n  hint: \"Check the Key Metrics cards at the top of the dashboard.\"\n}, {\n  question: \"In FY2024, 'Still Sales' revenue was approximately what percentage of the 'Gross Profit'?\",\n  options: [\"Approx. 65%\", \"Approx. 72%\", \"Approx. 81%\", \"Approx. 90%\"],\n  correctAnswer: \"Approx. 81%\",\n  hint: \"Compare data from the 'Revenue Composition' and 'Profitability Analysis' charts.\"\n}, {\n  question: \"What was the operating margin for FY2024? (Income from Operations / Net Sales)\",\n  options: [\"10.1%\", \"11.5%\", \"13.3%\", \"15.2%\"],\n  correctAnswer: \"13.3%\",\n  hint: \"Use data from the 'Profitability Analysis' chart and the top Key Metrics cards.\"\n}, {\n  question: \"How did the sales volume for 'Still' beverages change from FY2023 to FY2024?\",\n  options: [\"Increased\", \"Decreased\", \"Remained the same\"],\n  correctAnswer: \"Decreased\",\n  hint: \"Use the filter on the 'Bottle/Can Sales Volume' chart to compare the two years.\"\n}, {\n  question: \"Which year had the lowest Gross Profit according to the trend chart?\",\n  options: [\"2022\", \"2023\", \"2024\"],\n  correctAnswer: \"2022\",\n  hint: \"Filter the 'Performance Trend' chart to show 'Gross Profit'.\"\n}, {\n  question: \"What were the Selling, Delivery & Administrative (SD&A) expenses as a percentage of Net Sales in FY2024?\",\n  options: [\"21.5%\", \"26.6%\", \"29.8%\", \"33.1%\"],\n  correctAnswer: \"26.6%\",\n  hint: \"Use data from the 'Profitability Analysis' chart and the top Key Metrics cards.\"\n}, {\n  question: \"How did 'Other Sales' revenue change from FY2023 to FY2024?\",\n  options: [\"Increased by 7.5%\", \"Decreased by 7.5%\", \"Stayed the same\", \"Increased by 2.3%\"],\n  correctAnswer: \"Decreased by 7.5%\",\n  hint: \"Look at the 'Net Sales Breakdown' chart.\"\n}, {\n  question: \"What was the ratio of Gross Profit to SD&A Expenses in FY2024? (i.e., for every dollar of SD&A expense, how much gross profit was generated?)\",\n  options: [\"1.20\", \"1.35\", \"1.50\", \"1.75\"],\n  correctAnswer: \"1.50\",\n  hint: \"Divide Gross Profit by SD&A Expenses from the 'Profitability Analysis' chart for FY2024.\"\n}, {\n  question: \"Which revenue category, 'Sparkling Sales' or 'Still Sales', saw a larger absolute increase in net sales from FY2023 to FY2024?\",\n  options: [\"Sparkling Sales\", \"Still Sales\"],\n  correctAnswer: \"Sparkling Sales\",\n  hint: \"Analyze the 'Revenue Composition' chart.\"\n}, {\n  question: \"What was the approximate total bottle/can sales volume (in thousands of cases) for FY2023?\",\n  options: [\"355,367\", \"353,103\", \"345,821\", \"360,112\"],\n  correctAnswer: \"355,367\",\n  hint: \"Filter the 'Bottle/Can Sales Volume' chart to FY2023 and sum the values.\"\n}];\n\n// --- Helper Components ---\nconst Card = ({\n  title,\n  children,\n  icon\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"bg-white rounded-xl shadow-lg p-3 md:p-4 flex flex-col transition-all duration-300 hover:shadow-2xl hover:scale-105 border border-gray-100\",\n  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center mb-3\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-2 bg-red-100 rounded-full\",\n      children: icon\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 45\n    }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-lg font-bold text-gray-800 ml-3\",\n      children: title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 102\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 85,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex-grow\",\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 86,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 84,\n  columnNumber: 3\n}, this);\n_c = Card;\nconst StatCard = ({\n  title,\n  value,\n  change,\n  icon\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"bg-white rounded-xl shadow-lg p-4 transition-all duration-300 hover:shadow-2xl hover:scale-105 border border-gray-100\",\n  children: /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3 bg-red-100 rounded-full\",\n      children: icon\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 175\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ml-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm font-medium text-gray-500\",\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 254\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-2xl font-bold text-gray-900\",\n        children: value\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 314\n      }, this), change && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: `text-sm font-medium ${change.startsWith('+') ? 'text-green-600' : 'text-red-600'}`,\n        children: change\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 384\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 232\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 91,\n    columnNumber: 140\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 91,\n  columnNumber: 5\n}, this);\n_c2 = StatCard;\nconst CustomTooltip = ({\n  active,\n  payload,\n  label,\n  formatter\n}) => {\n  if (active && payload && payload.length) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white p-4 rounded-lg shadow-lg border border-gray-200\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"font-bold text-gray-800\",\n        children: label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 87\n      }, this), payload.map((entry, index) => /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: entry.color\n        },\n        children: `${entry.name}: ${formatter(entry.value)}`\n      }, `item-${index}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 169\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 13\n    }, this);\n  }\n  return null;\n};\n\n// --- Main App Component ---\n_c3 = CustomTooltip;\nexport default function App() {\n  _s();\n  const [showInstructions, setShowInstructions] = useState(true);\n  const [userAnswers, setUserAnswers] = useState({});\n  const [showResults, setShowResults] = useState(false);\n  const [score, setScore] = useState(0);\n  const [selectedVolumeYear, setSelectedVolumeYear] = useState('2024');\n  const [selectedTrendMetric, setSelectedTrendMetric] = useState('Net Sales');\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [showHint, setShowHint] = useState(false);\n  const [showHintConfirm, setShowHintConfirm] = useState(false);\n  const [hintsUsed, setHintsUsed] = useState(0);\n  const [taskCompleted, setTaskCompleted] = useState(false);\n  const [points, setPoints] = useState(30000);\n  useEffect(() => {\n    if (showResults) {\n      let correctCount = 0;\n      quizQuestions.forEach((q, index) => {\n        if (userAnswers[index] === q.correctAnswer) correctCount++;\n      });\n      setScore(correctCount);\n\n      // Check if task is completed (8+ correct answers OR hints used)\n      if (correctCount >= 8 || hintsUsed > 0) {\n        setTaskCompleted(true);\n      }\n    }\n  }, [showResults, userAnswers, hintsUsed]);\n  const handleOptionChange = option => setUserAnswers({\n    ...userAnswers,\n    [currentQuestion]: option\n  });\n  const handleSubmit = e => {\n    e.preventDefault();\n    setShowResults(true);\n  };\n  const resetQuiz = () => {\n    setUserAnswers({});\n    setShowResults(false);\n    setScore(0);\n    setCurrentQuestion(0);\n    setShowHint(false);\n    setShowHintConfirm(false);\n    setHintsUsed(0);\n    setTaskCompleted(false);\n    setPoints(30000);\n  };\n  const handleHintRequest = () => {\n    setShowHintConfirm(true);\n  };\n  const confirmHintUse = () => {\n    setHintsUsed(hintsUsed + 1);\n    setPoints(points - 1500);\n    setShowHint(true);\n    setShowHintConfirm(false);\n    setTaskCompleted(true); // Task completed when hint is used\n  };\n  const cancelHintUse = () => {\n    setShowHintConfirm(false);\n  };\n  const formatCurrency = value => `$${(value / 1000).toFixed(2)}M`;\n  const formatYAxis = tickItem => `$${(tickItem / 1000000).toFixed(1)}B`;\n  const netSalesChange = (financialData['2024'].netSales - financialData['2023'].netSales) / financialData['2023'].netSales * 100;\n  const grossProfitChange = (financialData['2024'].grossProfit - financialData['2023'].grossProfit) / financialData['2023'].grossProfit * 100;\n  const netIncomeChange = (financialData['2024'].netIncome - financialData['2023'].netIncome) / financialData['2023'].netIncome * 100;\n  const progress = (currentQuestion + 1) / quizQuestions.length * 100;\n\n  // Instructions Overlay Component\n  const InstructionsOverlay = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-red-700 to-red-600 text-white p-6 rounded-t-2xl\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-green-500 text-xs px-2 py-1 rounded-full mr-3 font-bold\",\n              children: \"LIVE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-xl font-bold\",\n              children: \"THE LEDGER OF LEGENDS\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-yellow-300 text-sm\",\n            children: \"\\u26A1 30,000 PTS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: \"https://images.unsplash.com/photo-1554224155-6726b3ff858f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80\",\n          alt: \"Financial Dashboard\",\n          className: \"w-full h-48 object-cover\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute bottom-4 left-4 bg-black bg-opacity-70 text-white px-3 py-1 rounded text-sm\",\n          children: \"\\uD83D\\uDCCA Mission Brief\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6 space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg font-bold text-gray-800 mb-2\",\n            children: \"Mission Brief\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 text-sm leading-relaxed\",\n            children: \"Your team must analyze the dashboard of financial data and correctly answer a series of questions. Study the charts, metrics, and trends carefully to succeed in this data challenge.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5\",\n              children: \"1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-semibold text-gray-800 text-sm\",\n                children: \"How to Play\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 text-xs\",\n                children: \"Explore the charts. You will be presented with a dashboard showing key year's sales data for the Coca-Cola Consolidated company.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5\",\n              children: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-semibold text-gray-800 text-sm\",\n                children: \"Analyze the Dashboard\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 text-xs\",\n                children: \"Based on the data, you must answer 10 questions about sales trends, profitability, and financial performance.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5\",\n              children: \"3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-semibold text-gray-800 text-sm\",\n                children: \"Pass with 70%\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 text-xs\",\n                children: \"To pass this challenge, you must answer at least 7 out of the 10 questions correctly.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5\",\n              children: \"4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-semibold text-gray-800 text-sm\",\n                children: \"Hints are Available\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 text-xs\",\n                children: \"If you come into that HINT, you can reveal the data and see the graphs. Use them wisely to understand the financial trends.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5\",\n              children: \"5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-semibold text-gray-800 text-sm\",\n                children: \"Time to Analyze\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 text-xs\",\n                children: \"Take your time examining the charts and understanding the data. The quiz will guide you through the data and questions one by one.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded-r-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-yellow-600 mr-2\",\n              children: \"\\uD83D\\uDCA1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-semibold text-yellow-800 text-sm\",\n                children: \"Pro Tip\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-yellow-700 text-xs\",\n                children: \"Study each chart carefully and look for trends, percentages, and year-over-year changes before starting the quiz.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pt-4\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowInstructions(false),\n            className: \"w-full bg-green-600 hover:bg-green-700 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl\",\n            children: \"START TASK\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 177,\n    columnNumber: 5\n  }, this);\n  if (showInstructions) {\n    return /*#__PURE__*/_jsxDEV(InstructionsOverlay, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 279,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-gray-50 min-h-screen font-sans text-gray-700\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"bg-red-700 text-white py-2 px-3 shadow-md sticky top-0 z-10\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto max-w-7xl flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-lg font-bold mr-4\",\n            children: \"Coca-Cola Consolidated\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-red-200 hidden md:inline\",\n            children: \"FY2024 Performance Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n          src: \"https://upload.wikimedia.org/wikipedia/commons/thumb/2/24/Coca-Cola_logo.svg/2560px-Coca-Cola_logo.svg.png\",\n          alt: \"Coca-Cola Logo\",\n          className: \"h-6 hidden sm:block\",\n          style: {\n            filter: 'brightness(0) invert(1)'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 284,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"container mx-auto max-w-7xl p-3 md:p-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Net Sales (FY24)\",\n          value: `$${(financialData['2024'].netSales / 1000000).toFixed(2)}B`,\n          change: `${netSalesChange > 0 ? '+' : ''}${netSalesChange.toFixed(1)}% vs FY23`,\n          icon: /*#__PURE__*/_jsxDEV(TrendingUp, {\n            className: \"h-8 w-8 text-red-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 202\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Gross Profit (FY24)\",\n          value: `$${(financialData['2024'].grossProfit / 1000000).toFixed(2)}B`,\n          change: `${grossProfitChange > 0 ? '+' : ''}${grossProfitChange.toFixed(1)}% vs FY23`,\n          icon: /*#__PURE__*/_jsxDEV(DollarSign, {\n            className: \"h-8 w-8 text-red-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 214\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Net Income (FY24)\",\n          value: `$${(financialData['2024'].netIncome / 1000).toFixed(2)}M`,\n          change: `${netIncomeChange > 0 ? '+' : ''}${netIncomeChange.toFixed(1)}% vs FY23`,\n          icon: /*#__PURE__*/_jsxDEV(CheckCircle, {\n            className: \"h-8 w-8 text-red-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 203\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-4 mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2\",\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            title: \"Performance Trend (in Millions)\",\n            icon: /*#__PURE__*/_jsxDEV(TrendingUp, {\n              className: \"text-red-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 69\n            }, this),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-gray-500 mr-2 flex items-center justify-center\",\n                children: [/*#__PURE__*/_jsxDEV(Filter, {\n                  size: 16,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 115\n                }, this), \" Filter Metric:\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 25\n              }, this), ['Net Sales', 'Gross Profit', 'Net Income'].map(metric => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setSelectedTrendMetric(metric),\n                className: `px-3 py-1 text-sm rounded-full mr-2 transition-colors ${selectedTrendMetric === metric ? 'bg-red-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`,\n                children: metric\n              }, metric, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 85\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n              width: \"100%\",\n              height: 250,\n              children: /*#__PURE__*/_jsxDEV(AreaChart, {\n                data: historicalPerformance,\n                children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                  strokeDasharray: \"3 3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 108\n                }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                  dataKey: \"year\",\n                  tick: {\n                    fill: '#4B5563'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 147\n                }, this), /*#__PURE__*/_jsxDEV(YAxis, {\n                  tickFormatter: val => `$${(val / 1000).toFixed(1)}B`,\n                  tick: {\n                    fill: '#4B5563'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 198\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  content: /*#__PURE__*/_jsxDEV(CustomTooltip, {\n                    formatter: val => `$${val.toLocaleString()}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 308,\n                    columnNumber: 306\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 288\n                }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 375\n                }, this), /*#__PURE__*/_jsxDEV(Area, {\n                  type: \"monotone\",\n                  dataKey: selectedTrendMetric,\n                  stroke: \"#B91C1C\",\n                  fill: \"#B91C1C\",\n                  fillOpacity: 0.7\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 385\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 68\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          title: \"Profitability Analysis (FY24)\",\n          icon: /*#__PURE__*/_jsxDEV(BarChart3, {\n            className: \"text-red-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 63\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 250,\n            children: /*#__PURE__*/_jsxDEV(BarChart, {\n              data: [profitabilityData[1]],\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\",\n                vertical: false\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 104\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"name\",\n                tick: {\n                  fill: '#4B5563'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 160\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {\n                tickFormatter: formatYAxis,\n                tick: {\n                  fill: '#4B5563'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 211\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                content: /*#__PURE__*/_jsxDEV(CustomTooltip, {\n                  formatter: formatCurrency\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 293\n                }, this),\n                cursor: {\n                  fill: 'rgba(239, 68, 68, 0.1)'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 275\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 383\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"Gross Profit\",\n                fill: \"#B91C1C\",\n                radius: [4, 4, 0, 0]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 393\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"SD&A Expenses\",\n                fill: \"#F87171\",\n                radius: [4, 4, 0, 0]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 460\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"Income from Operations\",\n                fill: \"#FECACA\",\n                radius: [4, 4, 0, 0]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 528\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 64\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-4 mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          title: \"Revenue Composition\",\n          icon: /*#__PURE__*/_jsxDEV(BarChart3, {\n            className: \"text-red-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 53\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 250,\n            children: /*#__PURE__*/_jsxDEV(BarChart, {\n              data: revenueCompositionData,\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\",\n                vertical: false\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 104\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"name\",\n                tick: {\n                  fill: '#4B5563'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 160\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {\n                tickFormatter: formatYAxis,\n                tick: {\n                  fill: '#4B5563'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 211\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                content: /*#__PURE__*/_jsxDEV(CustomTooltip, {\n                  formatter: formatCurrency\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 293\n                }, this),\n                cursor: {\n                  fill: 'rgba(239, 68, 68, 0.1)'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 275\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 383\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"Sparkling Sales\",\n                fill: \"#B91C1C\",\n                radius: [4, 4, 0, 0]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 393\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"Still Sales\",\n                fill: \"#F87171\",\n                radius: [4, 4, 0, 0]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 463\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 64\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          title: \"Net Sales Breakdown (FY24)\",\n          icon: /*#__PURE__*/_jsxDEV(Layers3, {\n            className: \"text-red-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 60\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 250,\n            children: /*#__PURE__*/_jsxDEV(BarChart, {\n              data: [netSalesBreakdown[1]],\n              layout: \"vertical\",\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\",\n                horizontal: false\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 122\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                type: \"number\",\n                tickFormatter: formatYAxis,\n                tick: {\n                  fill: '#4B5563'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 179\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {\n                type: \"category\",\n                dataKey: \"name\",\n                tick: {\n                  fill: '#4B5563'\n                },\n                width: 60\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 256\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                content: /*#__PURE__*/_jsxDEV(CustomTooltip, {\n                  formatter: formatCurrency\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 351\n                }, this),\n                cursor: {\n                  fill: 'rgba(239, 68, 68, 0.1)'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 333\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 440\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"Bottle/Can Sales\",\n                stackId: \"a\",\n                fill: \"#B91C1C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 450\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"Other Sales\",\n                stackId: \"a\",\n                fill: \"#F87171\",\n                radius: [0, 4, 4, 0]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 511\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 64\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          title: \"Bottle/Can Sales Volume\",\n          icon: /*#__PURE__*/_jsxDEV(PieIcon, {\n            className: \"text-red-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 57\n          }, this),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-gray-500 mr-2 flex items-center justify-center\",\n              children: [/*#__PURE__*/_jsxDEV(Filter, {\n                size: 16,\n                className: \"mr-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 141\n              }, this), \" Filter by Year:\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 51\n            }, this), ['2024', '2023'].map(year => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setSelectedVolumeYear(year),\n              className: `px-3 py-1 text-sm rounded-full mr-2 transition-colors ${selectedVolumeYear === year ? 'bg-red-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`,\n              children: year\n            }, year, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 231\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 220,\n            children: /*#__PURE__*/_jsxDEV(PieChart, {\n              children: [/*#__PURE__*/_jsxDEV(Pie, {\n                data: getSalesVolumeData(selectedVolumeYear),\n                cx: \"50%\",\n                cy: \"50%\",\n                labelLine: false,\n                label: ({\n                  name,\n                  percent\n                }) => `${name} ${(percent * 100).toFixed(0)}%`,\n                outerRadius: 100,\n                fill: \"#8884d8\",\n                dataKey: \"value\",\n                children: getSalesVolumeData(selectedVolumeYear).map((entry, index) => /*#__PURE__*/_jsxDEV(Cell, {\n                  fill: COLORS[index % COLORS.length]\n                }, `cell-${index}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 345\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 74\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                content: ({\n                  active,\n                  payload\n                }) => {\n                  if (active && payload && payload.length) {\n                    const data = payload[0].payload;\n                    const total = getSalesVolumeData(selectedVolumeYear).reduce((s, e) => s + e.value, 0);\n                    return /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white p-3 rounded-lg shadow-lg border\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"font-bold\",\n                        children: data.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 324,\n                        columnNumber: 693\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        style: {\n                          color: payload[0].fill\n                        },\n                        children: [\"Volume: \", data.value.toLocaleString(), \" cases\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 324,\n                        columnNumber: 733\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: [(data.value / total * 100).toFixed(1), \"% of Total\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 324,\n                        columnNumber: 818\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 324,\n                      columnNumber: 635\n                    }, this);\n                  }\n                  return null;\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 421\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 938\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 64\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        id: \"quiz-section\",\n        className: \"bg-white rounded-xl shadow-lg p-4 md:p-5 border border-gray-100\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(HelpCircle, {\n              className: \"text-red-600 h-8 w-8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-800 ml-3\",\n              children: \"Data Challenge\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 13\n          }, this), !showResults && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowHint(!showHint),\n              className: \"text-gray-500 hover:text-red-600 transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(Lightbulb, {\n                size: 24\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 167\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 57\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 31\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 11\n        }, this), !showResults ? /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full bg-gray-200 rounded-full h-2.5 mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-red-600 h-2.5 rounded-full\",\n              style: {\n                width: `${progress}%`,\n                transition: 'width 0.5s ease-in-out'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-center text-sm text-gray-500 mb-4\",\n            children: [\"Question \", currentQuestion + 1, \" of \", quizQuestions.length]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 15\n          }, this), showHint && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-4 rounded-r-lg\",\n            role: \"alert\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"font-bold\",\n              children: \"Hint\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 139\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: quizQuestions[currentQuestion].hint\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 172\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 28\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 min-h-[180px] flex flex-col justify-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"font-semibold text-xl text-gray-800 mb-4 text-center\",\n              children: quizQuestions[currentQuestion].question\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: quizQuestions[currentQuestion].options.map((option, i) => /*#__PURE__*/_jsxDEV(\"label\", {\n                className: `flex items-center p-4 rounded-lg hover:bg-red-100 cursor-pointer transition-colors border-2 ${userAnswers[currentQuestion] === option ? 'bg-red-100 border-red-500' : 'bg-gray-50 border-gray-200'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  name: `question-${currentQuestion}`,\n                  value: option,\n                  checked: userAnswers[currentQuestion] === option,\n                  onChange: () => handleOptionChange(option),\n                  className: \"form-radio h-5 w-5 text-red-600 focus:ring-red-500 border-gray-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-4 text-gray-700 text-lg\",\n                  children: option\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 352,\n                  columnNumber: 23\n                }, this)]\n              }, i, true, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center mt-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                setCurrentQuestion(q => Math.max(0, q - 1));\n                setShowHint(false);\n              },\n              disabled: currentQuestion === 0,\n              className: \"bg-gray-300 text-gray-700 font-bold py-2 px-6 rounded-full hover:bg-gray-400 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(ArrowLeft, {\n                size: 16,\n                className: \"mr-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 314\n              }, this), \" Previous\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 17\n            }, this), currentQuestion < quizQuestions.length - 1 ? /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                setCurrentQuestion(q => Math.min(quizQuestions.length - 1, q + 1));\n                setShowHint(false);\n              },\n              className: \"bg-red-600 text-white font-bold py-2 px-6 rounded-full hover:bg-red-700 transition-colors flex items-center\",\n              children: [\"Next \", /*#__PURE__*/_jsxDEV(ArrowRight, {\n                size: 16,\n                className: \"ml-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 258\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleSubmit,\n              className: \"bg-green-600 text-white font-bold py-2 px-6 rounded-full hover:bg-green-700 transition-colors\",\n              children: \"Submit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-3xl font-bold text-gray-800 mb-2\",\n            children: \"Challenge Complete!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg text-gray-600 mb-4\",\n            children: \"You scored:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `text-6xl font-bold mb-4 ${score >= 7 ? 'text-green-500' : 'text-red-500'}`,\n            children: [score, \" / \", quizQuestions.length]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `p-4 rounded-lg text-white font-bold text-2xl ${score >= 7 ? 'bg-green-500' : 'bg-red-500'}`,\n            children: score >= 7 ? 'Congratulations, You Passed!' : 'Keep Analyzing, Try Again!'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center mt-8\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: resetQuiz,\n              className: \"bg-gray-600 text-white font-bold py-3 px-8 rounded-full hover:bg-gray-700 transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-1\",\n              children: \"Try Again\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 294,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"bg-gray-800 text-white text-center p-3 mt-4\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm\",\n        children: \"Dashboard created based on the Coca-Cola Consolidated 2024 Annual Report. For educational purposes only.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 387,\n        columnNumber: 71\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 387,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 283,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"mSWuYgXI+V5p8tnzqEBgmllht34=\");\n_c4 = App;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"Card\");\n$RefreshReg$(_c2, \"StatCard\");\n$RefreshReg$(_c3, \"CustomTooltip\");\n$RefreshReg$(_c4, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON><PERSON>", "Bar", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "AreaChart", "Area", "HelpCircle", "TrendingUp", "DollarSign", "CheckCircle", "BarChart3", "PieIcon", "Layers3", "Filter", "ArrowLeft", "ArrowRight", "Lightbulb", "jsxDEV", "_jsxDEV", "financialData", "netSales", "bottleCanNetSales", "otherNetSales", "sparklingNetSales", "stillNetSales", "grossProfit", "incomeFromOperations", "netIncome", "bottleCanSalesVolume", "sparkling", "still", "total", "sdeaExpenses", "historicalPerformance", "year", "getSalesVolumeData", "name", "value", "netSalesBreakdown", "profitabilityData", "revenueCompositionData", "COLORS", "quizQuestions", "question", "options", "<PERSON><PERSON><PERSON><PERSON>", "hint", "Card", "title", "children", "icon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "StatCard", "change", "startsWith", "_c2", "CustomTooltip", "active", "payload", "label", "formatter", "length", "map", "entry", "index", "style", "color", "_c3", "App", "_s", "showInstructions", "setShowInstructions", "userAnswers", "setUserAnswers", "showResults", "setShowResults", "score", "setScore", "selectedVolumeYear", "setSelectedVolumeYear", "selectedTrendMetric", "setSelectedTrendMetric", "currentQuestion", "setCurrentQuestion", "showHint", "setShowHint", "showHintConfirm", "setShowHintConfirm", "hintsUsed", "setHintsUsed", "taskCompleted", "setTaskCompleted", "points", "setPoints", "correctCount", "for<PERSON>ach", "q", "handleOptionChange", "option", "handleSubmit", "e", "preventDefault", "resetQuiz", "handleHintRequest", "confirmHintUse", "cancelHintUse", "formatCurrency", "toFixed", "formatYAxis", "tickItem", "netSalesChange", "grossProfitChange", "netIncomeChange", "progress", "InstructionsOverlay", "src", "alt", "onClick", "filter", "size", "metric", "width", "height", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "tick", "fill", "tick<PERSON><PERSON><PERSON><PERSON>", "val", "content", "toLocaleString", "type", "stroke", "fillOpacity", "vertical", "cursor", "radius", "layout", "horizontal", "stackId", "cx", "cy", "labelLine", "percent", "outerRadius", "reduce", "s", "id", "transition", "role", "i", "checked", "onChange", "Math", "max", "disabled", "min", "_c4", "$RefreshReg$"], "sources": ["D:/HCCB/Dashboard/coca-cola-dashboard/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer, <PERSON><PERSON><PERSON>, <PERSON>, Cell, AreaChart, Area } from 'recharts';\nimport { HelpCircle, TrendingUp, DollarSign, CheckCircle, Bar<PERSON>hart3, <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>, <PERSON><PERSON>3, <PERSON><PERSON>, <PERSON>Lef<PERSON>, ArrowRight, Lightbulb } from 'lucide-react';\n\n// --- Data extracted from the Annual Report (in thousands) ---\nconst financialData = {\n  '2024': {\n    netSales: 6899716,\n    bottleCanNetSales: 6333316,\n    otherNetSales: 566400,\n    sparklingNetSales: 4106073,\n    stillNetSales: 2227243,\n    grossProfit: 2753179,\n    incomeFromOperations: 920350,\n    netIncome: 633125,\n    bottleCanSalesVolume: { sparkling: 266686, still: 86417, total: 353103 },\n    sdeaExpenses: 1832829,\n  },\n  '2023': {\n    netSales: 6653858,\n    bottleCanNetSales: 6041772,\n    otherNetSales: 612086,\n    sparklingNetSales: 3892133,\n    stillNetSales: 2149639,\n    grossProfit: 2598711,\n    incomeFromOperations: 834451,\n    netIncome: 408375,\n    bottleCanSalesVolume: { sparkling: 263872, still: 91495, total: 355367 },\n    sdeaExpenses: 1764260,\n  },\n  '2022': {\n    netSales: 6200957,\n    grossProfit: 2277954,\n    incomeFromOperations: 641047,\n    netIncome: 430158,\n  }\n};\n\n// --- Chart Data Preparation ---\nconst historicalPerformance = [\n  { year: '2022', 'Net Sales': financialData['2022'].netSales / 1000, 'Gross Profit': financialData['2022'].grossProfit / 1000, 'Net Income': financialData['2022'].netIncome / 1000 },\n  { year: '2023', 'Net Sales': financialData['2023'].netSales / 1000, 'Gross Profit': financialData['2023'].grossProfit / 1000, 'Net Income': financialData['2023'].netIncome / 1000 },\n  { year: '2024', 'Net Sales': financialData['2024'].netSales / 1000, 'Gross Profit': financialData['2024'].grossProfit / 1000, 'Net Income': financialData['2024'].netIncome / 1000 },\n];\n\nconst getSalesVolumeData = (year) => [\n  { name: 'Sparkling', value: financialData[year].bottleCanSalesVolume.sparkling },\n  { name: 'Still', value: financialData[year].bottleCanSalesVolume.still },\n];\n\nconst netSalesBreakdown = [\n    { name: 'FY2023', 'Bottle/Can Sales': financialData['2023'].bottleCanNetSales, 'Other Sales': financialData['2023'].otherNetSales },\n    { name: 'FY2024', 'Bottle/Can Sales': financialData['2024'].bottleCanNetSales, 'Other Sales': financialData['2024'].otherNetSales },\n];\n\nconst profitabilityData = [\n    { name: 'FY2023', 'Gross Profit': financialData['2023'].grossProfit, 'SD&A Expenses': financialData['2023'].sdeaExpenses, 'Income from Operations': financialData['2023'].incomeFromOperations },\n    { name: 'FY2024', 'Gross Profit': financialData['2024'].grossProfit, 'SD&A Expenses': financialData['2024'].sdeaExpenses, 'Income from Operations': financialData['2024'].incomeFromOperations },\n];\n\nconst revenueCompositionData = [\n    { name: 'FY2023', 'Sparkling Sales': financialData['2023'].sparklingNetSales, 'Still Sales': financialData['2023'].stillNetSales },\n    { name: 'FY2024', 'Sparkling Sales': financialData['2024'].sparklingNetSales, 'Still Sales': financialData['2024'].stillNetSales },\n];\n\nconst COLORS = ['#B91C1C', '#F87171'];\n\n// --- Revised Quiz Questions ---\nconst quizQuestions = [\n  { question: \"Which metric saw a higher percentage growth from FY2023 to FY2024?\", options: [\"Net Sales\", \"Gross Profit\", \"Net Income\"], correctAnswer: \"Net Income\", hint: \"Check the Key Metrics cards at the top of the dashboard.\" },\n  { question: \"In FY2024, 'Still Sales' revenue was approximately what percentage of the 'Gross Profit'?\", options: [\"Approx. 65%\", \"Approx. 72%\", \"Approx. 81%\", \"Approx. 90%\"], correctAnswer: \"Approx. 81%\", hint: \"Compare data from the 'Revenue Composition' and 'Profitability Analysis' charts.\" },\n  { question: \"What was the operating margin for FY2024? (Income from Operations / Net Sales)\", options: [\"10.1%\", \"11.5%\", \"13.3%\", \"15.2%\"], correctAnswer: \"13.3%\", hint: \"Use data from the 'Profitability Analysis' chart and the top Key Metrics cards.\" },\n  { question: \"How did the sales volume for 'Still' beverages change from FY2023 to FY2024?\", options: [\"Increased\", \"Decreased\", \"Remained the same\"], correctAnswer: \"Decreased\", hint: \"Use the filter on the 'Bottle/Can Sales Volume' chart to compare the two years.\" },\n  { question: \"Which year had the lowest Gross Profit according to the trend chart?\", options: [\"2022\", \"2023\", \"2024\"], correctAnswer: \"2022\", hint: \"Filter the 'Performance Trend' chart to show 'Gross Profit'.\" },\n  { question: \"What were the Selling, Delivery & Administrative (SD&A) expenses as a percentage of Net Sales in FY2024?\", options: [\"21.5%\", \"26.6%\", \"29.8%\", \"33.1%\"], correctAnswer: \"26.6%\", hint: \"Use data from the 'Profitability Analysis' chart and the top Key Metrics cards.\" },\n  { question: \"How did 'Other Sales' revenue change from FY2023 to FY2024?\", options: [\"Increased by 7.5%\", \"Decreased by 7.5%\", \"Stayed the same\", \"Increased by 2.3%\"], correctAnswer: \"Decreased by 7.5%\", hint: \"Look at the 'Net Sales Breakdown' chart.\" },\n  { question: \"What was the ratio of Gross Profit to SD&A Expenses in FY2024? (i.e., for every dollar of SD&A expense, how much gross profit was generated?)\", options: [\"1.20\", \"1.35\", \"1.50\", \"1.75\"], correctAnswer: \"1.50\", hint: \"Divide Gross Profit by SD&A Expenses from the 'Profitability Analysis' chart for FY2024.\" },\n  { question: \"Which revenue category, 'Sparkling Sales' or 'Still Sales', saw a larger absolute increase in net sales from FY2023 to FY2024?\", options: [\"Sparkling Sales\", \"Still Sales\"], correctAnswer: \"Sparkling Sales\", hint: \"Analyze the 'Revenue Composition' chart.\" },\n  { question: \"What was the approximate total bottle/can sales volume (in thousands of cases) for FY2023?\", options: [\"355,367\", \"353,103\", \"345,821\", \"360,112\"], correctAnswer: \"355,367\", hint: \"Filter the 'Bottle/Can Sales Volume' chart to FY2023 and sum the values.\" },\n];\n\n// --- Helper Components ---\nconst Card = ({ title, children, icon }) => (\n  <div className=\"bg-white rounded-xl shadow-lg p-3 md:p-4 flex flex-col transition-all duration-300 hover:shadow-2xl hover:scale-105 border border-gray-100\">\n    <div className=\"flex items-center mb-3\"><div className=\"p-2 bg-red-100 rounded-full\">{icon}</div><h2 className=\"text-lg font-bold text-gray-800 ml-3\">{title}</h2></div>\n    <div className=\"flex-grow\">{children}</div>\n  </div>\n);\n\nconst StatCard = ({ title, value, change, icon }) => (\n    <div className=\"bg-white rounded-xl shadow-lg p-4 transition-all duration-300 hover:shadow-2xl hover:scale-105 border border-gray-100\"><div className=\"flex items-center\"><div className=\"p-3 bg-red-100 rounded-full\">{icon}</div><div className=\"ml-4\"><p className=\"text-sm font-medium text-gray-500\">{title}</p><p className=\"text-2xl font-bold text-gray-900\">{value}</p>{change && <p className={`text-sm font-medium ${change.startsWith('+') ? 'text-green-600' : 'text-red-600'}`}>{change}</p>}</div></div></div>\n);\n\nconst CustomTooltip = ({ active, payload, label, formatter }) => {\n  if (active && payload && payload.length) {\n    return (<div className=\"bg-white p-4 rounded-lg shadow-lg border border-gray-200\"><p className=\"font-bold text-gray-800\">{label}</p>{payload.map((entry, index) => (<p key={`item-${index}`} style={{ color: entry.color }}>{`${entry.name}: ${formatter(entry.value)}`}</p>))}</div>);\n  }\n  return null;\n};\n\n// --- Main App Component ---\nexport default function App() {\n  const [showInstructions, setShowInstructions] = useState(true);\n  const [userAnswers, setUserAnswers] = useState({});\n  const [showResults, setShowResults] = useState(false);\n  const [score, setScore] = useState(0);\n  const [selectedVolumeYear, setSelectedVolumeYear] = useState('2024');\n  const [selectedTrendMetric, setSelectedTrendMetric] = useState('Net Sales');\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [showHint, setShowHint] = useState(false);\n  const [showHintConfirm, setShowHintConfirm] = useState(false);\n  const [hintsUsed, setHintsUsed] = useState(0);\n  const [taskCompleted, setTaskCompleted] = useState(false);\n  const [points, setPoints] = useState(30000);\n\n  useEffect(() => {\n    if (showResults) {\n      let correctCount = 0;\n      quizQuestions.forEach((q, index) => {\n        if (userAnswers[index] === q.correctAnswer) correctCount++;\n      });\n      setScore(correctCount);\n\n      // Check if task is completed (8+ correct answers OR hints used)\n      if (correctCount >= 8 || hintsUsed > 0) {\n        setTaskCompleted(true);\n      }\n    }\n  }, [showResults, userAnswers, hintsUsed]);\n\n  const handleOptionChange = (option) => setUserAnswers({ ...userAnswers, [currentQuestion]: option });\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    setShowResults(true);\n  };\n\n  const resetQuiz = () => {\n    setUserAnswers({});\n    setShowResults(false);\n    setScore(0);\n    setCurrentQuestion(0);\n    setShowHint(false);\n    setShowHintConfirm(false);\n    setHintsUsed(0);\n    setTaskCompleted(false);\n    setPoints(30000);\n  };\n\n  const handleHintRequest = () => {\n    setShowHintConfirm(true);\n  };\n\n  const confirmHintUse = () => {\n    setHintsUsed(hintsUsed + 1);\n    setPoints(points - 1500);\n    setShowHint(true);\n    setShowHintConfirm(false);\n    setTaskCompleted(true); // Task completed when hint is used\n  };\n\n  const cancelHintUse = () => {\n    setShowHintConfirm(false);\n  };\n\n  const formatCurrency = (value) => `$${(value / 1000).toFixed(2)}M`;\n  const formatYAxis = (tickItem) => `$${(tickItem / 1000000).toFixed(1)}B`;\n\n  const netSalesChange = ((financialData['2024'].netSales - financialData['2023'].netSales) / financialData['2023'].netSales) * 100;\n  const grossProfitChange = ((financialData['2024'].grossProfit - financialData['2023'].grossProfit) / financialData['2023'].grossProfit) * 100;\n  const netIncomeChange = ((financialData['2024'].netIncome - financialData['2023'].netIncome) / financialData['2023'].netIncome) * 100;\n\n  const progress = ((currentQuestion + 1) / quizQuestions.length) * 100;\n\n  // Instructions Overlay Component\n  const InstructionsOverlay = () => (\n    <div className=\"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n        {/* Header */}\n        <div className=\"bg-gradient-to-r from-red-700 to-red-600 text-white p-6 rounded-t-2xl\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center\">\n              <div className=\"bg-green-500 text-xs px-2 py-1 rounded-full mr-3 font-bold\">LIVE</div>\n              <h1 className=\"text-xl font-bold\">THE LEDGER OF LEGENDS</h1>\n            </div>\n            <div className=\"text-yellow-300 text-sm\">⚡ 30,000 PTS</div>\n          </div>\n        </div>\n\n        {/* Hero Image */}\n        <div className=\"relative\">\n          <img\n            src=\"https://images.unsplash.com/photo-1554224155-6726b3ff858f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80\"\n            alt=\"Financial Dashboard\"\n            className=\"w-full h-48 object-cover\"\n          />\n          <div className=\"absolute bottom-4 left-4 bg-black bg-opacity-70 text-white px-3 py-1 rounded text-sm\">\n            📊 Mission Brief\n          </div>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-6 space-y-4\">\n          <div>\n            <h2 className=\"text-lg font-bold text-gray-800 mb-2\">Mission Brief</h2>\n            <p className=\"text-gray-600 text-sm leading-relaxed\">\n              Your team must analyze the dashboard of financial data and correctly answer a series of questions.\n              Study the charts, metrics, and trends carefully to succeed in this data challenge.\n            </p>\n          </div>\n\n          <div className=\"space-y-3\">\n            <div className=\"flex items-start\">\n              <div className=\"bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5\">1</div>\n              <div>\n                <h3 className=\"font-semibold text-gray-800 text-sm\">How to Play</h3>\n                <p className=\"text-gray-600 text-xs\">Explore the charts. You will be presented with a dashboard showing key year's sales data for the Coca-Cola Consolidated company.</p>\n              </div>\n            </div>\n\n            <div className=\"flex items-start\">\n              <div className=\"bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5\">2</div>\n              <div>\n                <h3 className=\"font-semibold text-gray-800 text-sm\">Analyze the Dashboard</h3>\n                <p className=\"text-gray-600 text-xs\">Based on the data, you must answer 10 questions about sales trends, profitability, and financial performance.</p>\n              </div>\n            </div>\n\n            <div className=\"flex items-start\">\n              <div className=\"bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5\">3</div>\n              <div>\n                <h3 className=\"font-semibold text-gray-800 text-sm\">Pass with 70%</h3>\n                <p className=\"text-gray-600 text-xs\">To pass this challenge, you must answer at least 7 out of the 10 questions correctly.</p>\n              </div>\n            </div>\n\n            <div className=\"flex items-start\">\n              <div className=\"bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5\">4</div>\n              <div>\n                <h3 className=\"font-semibold text-gray-800 text-sm\">Hints are Available</h3>\n                <p className=\"text-gray-600 text-xs\">If you come into that HINT, you can reveal the data and see the graphs. Use them wisely to understand the financial trends.</p>\n              </div>\n            </div>\n\n            <div className=\"flex items-start\">\n              <div className=\"bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5\">5</div>\n              <div>\n                <h3 className=\"font-semibold text-gray-800 text-sm\">Time to Analyze</h3>\n                <p className=\"text-gray-600 text-xs\">Take your time examining the charts and understanding the data. The quiz will guide you through the data and questions one by one.</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded-r-lg\">\n            <div className=\"flex items-center\">\n              <div className=\"text-yellow-600 mr-2\">💡</div>\n              <div>\n                <h4 className=\"font-semibold text-yellow-800 text-sm\">Pro Tip</h4>\n                <p className=\"text-yellow-700 text-xs\">Study each chart carefully and look for trends, percentages, and year-over-year changes before starting the quiz.</p>\n              </div>\n            </div>\n          </div>\n\n          {/* Start Button */}\n          <div className=\"pt-4\">\n            <button\n              onClick={() => setShowInstructions(false)}\n              className=\"w-full bg-green-600 hover:bg-green-700 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl\"\n            >\n              START TASK\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  if (showInstructions) {\n    return <InstructionsOverlay />;\n  }\n\n  return (\n    <div className=\"bg-gray-50 min-h-screen font-sans text-gray-700\">\n      <header className=\"bg-red-700 text-white py-2 px-3 shadow-md sticky top-0 z-10\">\n        <div className=\"container mx-auto max-w-7xl flex justify-between items-center\">\n          <div className=\"flex items-center\">\n            <h1 className=\"text-lg font-bold mr-4\">Coca-Cola Consolidated</h1>\n            <span className=\"text-xs text-red-200 hidden md:inline\">FY2024 Performance Dashboard</span>\n          </div>\n          <img src=\"https://upload.wikimedia.org/wikipedia/commons/thumb/2/24/Coca-Cola_logo.svg/2560px-Coca-Cola_logo.svg.png\" alt=\"Coca-Cola Logo\" className=\"h-6 hidden sm:block\" style={{filter: 'brightness(0) invert(1)'}}/>\n        </div>\n      </header>\n\n      <main className=\"container mx-auto max-w-7xl p-3 md:p-4\">\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\">\n          <StatCard title=\"Net Sales (FY24)\" value={`$${(financialData['2024'].netSales / 1000000).toFixed(2)}B`} change={`${netSalesChange > 0 ? '+' : ''}${netSalesChange.toFixed(1)}% vs FY23`} icon={<TrendingUp className=\"h-8 w-8 text-red-600\" />} />\n          <StatCard title=\"Gross Profit (FY24)\" value={`$${(financialData['2024'].grossProfit / 1000000).toFixed(2)}B`} change={`${grossProfitChange > 0 ? '+' : ''}${grossProfitChange.toFixed(1)}% vs FY23`} icon={<DollarSign className=\"h-8 w-8 text-red-600\" />} />\n          <StatCard title=\"Net Income (FY24)\" value={`$${(financialData['2024'].netIncome / 1000).toFixed(2)}M`} change={`${netIncomeChange > 0 ? '+' : ''}${netIncomeChange.toFixed(1)}% vs FY23`} icon={<CheckCircle className=\"h-8 w-8 text-red-600\" />} />\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-4 mb-4\">\n            <div className=\"lg:col-span-2\">\n                <Card title=\"Performance Trend (in Millions)\" icon={<TrendingUp className=\"text-red-600\" />}>\n                    <div className=\"text-center mb-4\">\n                        <span className=\"text-sm font-medium text-gray-500 mr-2 flex items-center justify-center\"><Filter size={16} className=\"mr-1\"/> Filter Metric:</span>\n                        {['Net Sales', 'Gross Profit', 'Net Income'].map(metric => (<button key={metric} onClick={() => setSelectedTrendMetric(metric)} className={`px-3 py-1 text-sm rounded-full mr-2 transition-colors ${selectedTrendMetric === metric ? 'bg-red-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}>{metric}</button>))}\n                    </div>\n                    <ResponsiveContainer width=\"100%\" height={250}><AreaChart data={historicalPerformance}><CartesianGrid strokeDasharray=\"3 3\" /><XAxis dataKey=\"year\" tick={{ fill: '#4B5563' }} /><YAxis tickFormatter={(val) => `$${(val/1000).toFixed(1)}B`} tick={{ fill: '#4B5563' }} /><Tooltip content={<CustomTooltip formatter={(val) => `$${val.toLocaleString()}`} />} /><Legend /><Area type=\"monotone\" dataKey={selectedTrendMetric} stroke=\"#B91C1C\" fill=\"#B91C1C\" fillOpacity={0.7} /></AreaChart></ResponsiveContainer>\n                </Card>\n            </div>\n            <Card title=\"Profitability Analysis (FY24)\" icon={<BarChart3 className=\"text-red-600\" />}>\n                <ResponsiveContainer width=\"100%\" height={250}><BarChart data={[profitabilityData[1]]}><CartesianGrid strokeDasharray=\"3 3\" vertical={false} /><XAxis dataKey=\"name\" tick={{ fill: '#4B5563' }} /><YAxis tickFormatter={formatYAxis} tick={{ fill: '#4B5563' }} /><Tooltip content={<CustomTooltip formatter={formatCurrency} />} cursor={{fill: 'rgba(239, 68, 68, 0.1)'}} /><Legend /><Bar dataKey=\"Gross Profit\" fill=\"#B91C1C\" radius={[4, 4, 0, 0]} /><Bar dataKey=\"SD&A Expenses\" fill=\"#F87171\" radius={[4, 4, 0, 0]} /><Bar dataKey=\"Income from Operations\" fill=\"#FECACA\" radius={[4, 4, 0, 0]} /></BarChart></ResponsiveContainer>\n            </Card>\n        </div>\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-4 mb-4\">\n            <Card title=\"Revenue Composition\" icon={<BarChart3 className=\"text-red-600\" />}>\n                <ResponsiveContainer width=\"100%\" height={250}><BarChart data={revenueCompositionData}><CartesianGrid strokeDasharray=\"3 3\" vertical={false} /><XAxis dataKey=\"name\" tick={{ fill: '#4B5563' }} /><YAxis tickFormatter={formatYAxis} tick={{ fill: '#4B5563' }} /><Tooltip content={<CustomTooltip formatter={formatCurrency} />} cursor={{fill: 'rgba(239, 68, 68, 0.1)'}} /><Legend /><Bar dataKey=\"Sparkling Sales\" fill=\"#B91C1C\" radius={[4, 4, 0, 0]} /><Bar dataKey=\"Still Sales\" fill=\"#F87171\" radius={[4, 4, 0, 0]} /></BarChart></ResponsiveContainer>\n            </Card>\n            <Card title=\"Net Sales Breakdown (FY24)\" icon={<Layers3 className=\"text-red-600\" />}>\n                <ResponsiveContainer width=\"100%\" height={250}><BarChart data={[netSalesBreakdown[1]]} layout=\"vertical\"><CartesianGrid strokeDasharray=\"3 3\" horizontal={false}/><XAxis type=\"number\" tickFormatter={formatYAxis} tick={{ fill: '#4B5563' }}/><YAxis type=\"category\" dataKey=\"name\" tick={{ fill: '#4B5563' }} width={60}/><Tooltip content={<CustomTooltip formatter={formatCurrency} />} cursor={{fill: 'rgba(239, 68, 68, 0.1)'}}/><Legend /><Bar dataKey=\"Bottle/Can Sales\" stackId=\"a\" fill=\"#B91C1C\" /><Bar dataKey=\"Other Sales\" stackId=\"a\" fill=\"#F87171\" radius={[0, 4, 4, 0]}/></BarChart></ResponsiveContainer>\n            </Card>\n            <Card title=\"Bottle/Can Sales Volume\" icon={<PieIcon className=\"text-red-600\" />}>\n                <div className=\"text-center mb-2\"><span className=\"text-sm font-medium text-gray-500 mr-2 flex items-center justify-center\"><Filter size={16} className=\"mr-1\"/> Filter by Year:</span>{['2024', '2023'].map(year => (<button key={year} onClick={() => setSelectedVolumeYear(year)} className={`px-3 py-1 text-sm rounded-full mr-2 transition-colors ${selectedVolumeYear === year ? 'bg-red-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}>{year}</button>))}</div>\n                <ResponsiveContainer width=\"100%\" height={220}><PieChart><Pie data={getSalesVolumeData(selectedVolumeYear)} cx=\"50%\" cy=\"50%\" labelLine={false} label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`} outerRadius={100} fill=\"#8884d8\" dataKey=\"value\">{getSalesVolumeData(selectedVolumeYear).map((entry, index) => (<Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />))}</Pie><Tooltip content={({ active, payload }) => {if (active && payload && payload.length) {const data = payload[0].payload; const total = getSalesVolumeData(selectedVolumeYear).reduce((s, e) => s + e.value, 0); return (<div className=\"bg-white p-3 rounded-lg shadow-lg border\"><p className=\"font-bold\">{data.name}</p><p style={{ color: payload[0].fill }}>Volume: {data.value.toLocaleString()} cases</p><p className=\"text-sm text-gray-600\">{((data.value / total) * 100).toFixed(1)}% of Total</p></div>); } return null;}} /><Legend /></PieChart></ResponsiveContainer>\n            </Card>\n        </div>\n\n        <div id=\"quiz-section\" className=\"bg-white rounded-xl shadow-lg p-4 md:p-5 border border-gray-100\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <div className=\"flex items-center\">\n              <HelpCircle className=\"text-red-600 h-8 w-8\" />\n              <h2 className=\"text-2xl font-bold text-gray-800 ml-3\">Data Challenge</h2>\n            </div>\n             {!showResults && <div className=\"relative\"><button onClick={() => setShowHint(!showHint)} className=\"text-gray-500 hover:text-red-600 transition-colors\"><Lightbulb size={24}/></button></div>}\n          </div>\n\n          {!showResults ? (\n            <div>\n              <div className=\"w-full bg-gray-200 rounded-full h-2.5 mb-4\">\n                <div className=\"bg-red-600 h-2.5 rounded-full\" style={{ width: `${progress}%`, transition: 'width 0.5s ease-in-out' }}></div>\n              </div>\n              <p className=\"text-center text-sm text-gray-500 mb-4\">Question {currentQuestion + 1} of {quizQuestions.length}</p>\n\n              {showHint && <div className=\"bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-4 rounded-r-lg\" role=\"alert\"><p className=\"font-bold\">Hint</p><p>{quizQuestions[currentQuestion].hint}</p></div>}\n\n              <div className=\"p-3 min-h-[180px] flex flex-col justify-center\">\n                <p className=\"font-semibold text-xl text-gray-800 mb-4 text-center\">{quizQuestions[currentQuestion].question}</p>\n                <div className=\"space-y-3\">\n                  {quizQuestions[currentQuestion].options.map((option, i) => (\n                    <label key={i} className={`flex items-center p-4 rounded-lg hover:bg-red-100 cursor-pointer transition-colors border-2 ${userAnswers[currentQuestion] === option ? 'bg-red-100 border-red-500' : 'bg-gray-50 border-gray-200'}`}>\n                      <input type=\"radio\" name={`question-${currentQuestion}`} value={option} checked={userAnswers[currentQuestion] === option} onChange={() => handleOptionChange(option)} className=\"form-radio h-5 w-5 text-red-600 focus:ring-red-500 border-gray-300\" />\n                      <span className=\"ml-4 text-gray-700 text-lg\">{option}</span>\n                    </label>\n                  ))}\n                </div>\n              </div>\n\n              <div className=\"flex justify-between items-center mt-6\">\n                <button onClick={() => {setCurrentQuestion(q => Math.max(0, q - 1)); setShowHint(false);}} disabled={currentQuestion === 0} className=\"bg-gray-300 text-gray-700 font-bold py-2 px-6 rounded-full hover:bg-gray-400 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center\"><ArrowLeft size={16} className=\"mr-1\"/> Previous</button>\n                {currentQuestion < quizQuestions.length - 1 ? (\n                  <button onClick={() => {setCurrentQuestion(q => Math.min(quizQuestions.length - 1, q + 1)); setShowHint(false);}} className=\"bg-red-600 text-white font-bold py-2 px-6 rounded-full hover:bg-red-700 transition-colors flex items-center\">Next <ArrowRight size={16} className=\"ml-1\"/></button>\n                ) : (\n                  <button onClick={handleSubmit} className=\"bg-green-600 text-white font-bold py-2 px-6 rounded-full hover:bg-green-700 transition-colors\">Submit</button>\n                )}\n              </div>\n            </div>\n          ) : (\n            <div className=\"text-center\">\n              <h3 className=\"text-3xl font-bold text-gray-800 mb-2\">Challenge Complete!</h3>\n              <p className=\"text-lg text-gray-600 mb-4\">You scored:</p>\n              <div className={`text-6xl font-bold mb-4 ${score >= 7 ? 'text-green-500' : 'text-red-500'}`}>\n                {score} / {quizQuestions.length}\n              </div>\n              <div className={`p-4 rounded-lg text-white font-bold text-2xl ${score >= 7 ? 'bg-green-500' : 'bg-red-500'}`}>\n                {score >= 7 ? 'Congratulations, You Passed!' : 'Keep Analyzing, Try Again!'}\n              </div>\n              <div className=\"text-center mt-8\">\n                <button onClick={resetQuiz} className=\"bg-gray-600 text-white font-bold py-3 px-8 rounded-full hover:bg-gray-700 transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-1\">\n                  Try Again\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      </main>\n\n      <footer className=\"bg-gray-800 text-white text-center p-3 mt-4\"><p className=\"text-sm\">Dashboard created based on the Coca-Cola Consolidated 2024 Annual Report. For educational purposes only.</p></footer>\n    </div>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,QAAQ,EAAEC,GAAG,EAAEC,KAAK,EAAEC,KAAK,EAAEC,aAAa,EAAEC,OAAO,EAAEC,MAAM,EAAEC,mBAAmB,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,IAAI,EAAEC,SAAS,EAAEC,IAAI,QAAQ,UAAU;AACjJ,SAASC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,WAAW,EAAEC,SAAS,EAAET,QAAQ,IAAIU,OAAO,EAAEC,OAAO,EAAEC,MAAM,EAAEC,SAAS,EAAEC,UAAU,EAAEC,SAAS,QAAQ,cAAc;;AAEjK;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,aAAa,GAAG;EACpB,MAAM,EAAE;IACNC,QAAQ,EAAE,OAAO;IACjBC,iBAAiB,EAAE,OAAO;IAC1BC,aAAa,EAAE,MAAM;IACrBC,iBAAiB,EAAE,OAAO;IAC1BC,aAAa,EAAE,OAAO;IACtBC,WAAW,EAAE,OAAO;IACpBC,oBAAoB,EAAE,MAAM;IAC5BC,SAAS,EAAE,MAAM;IACjBC,oBAAoB,EAAE;MAAEC,SAAS,EAAE,MAAM;MAAEC,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAO,CAAC;IACxEC,YAAY,EAAE;EAChB,CAAC;EACD,MAAM,EAAE;IACNZ,QAAQ,EAAE,OAAO;IACjBC,iBAAiB,EAAE,OAAO;IAC1BC,aAAa,EAAE,MAAM;IACrBC,iBAAiB,EAAE,OAAO;IAC1BC,aAAa,EAAE,OAAO;IACtBC,WAAW,EAAE,OAAO;IACpBC,oBAAoB,EAAE,MAAM;IAC5BC,SAAS,EAAE,MAAM;IACjBC,oBAAoB,EAAE;MAAEC,SAAS,EAAE,MAAM;MAAEC,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAO,CAAC;IACxEC,YAAY,EAAE;EAChB,CAAC;EACD,MAAM,EAAE;IACNZ,QAAQ,EAAE,OAAO;IACjBK,WAAW,EAAE,OAAO;IACpBC,oBAAoB,EAAE,MAAM;IAC5BC,SAAS,EAAE;EACb;AACF,CAAC;;AAED;AACA,MAAMM,qBAAqB,GAAG,CAC5B;EAAEC,IAAI,EAAE,MAAM;EAAE,WAAW,EAAEf,aAAa,CAAC,MAAM,CAAC,CAACC,QAAQ,GAAG,IAAI;EAAE,cAAc,EAAED,aAAa,CAAC,MAAM,CAAC,CAACM,WAAW,GAAG,IAAI;EAAE,YAAY,EAAEN,aAAa,CAAC,MAAM,CAAC,CAACQ,SAAS,GAAG;AAAK,CAAC,EACpL;EAAEO,IAAI,EAAE,MAAM;EAAE,WAAW,EAAEf,aAAa,CAAC,MAAM,CAAC,CAACC,QAAQ,GAAG,IAAI;EAAE,cAAc,EAAED,aAAa,CAAC,MAAM,CAAC,CAACM,WAAW,GAAG,IAAI;EAAE,YAAY,EAAEN,aAAa,CAAC,MAAM,CAAC,CAACQ,SAAS,GAAG;AAAK,CAAC,EACpL;EAAEO,IAAI,EAAE,MAAM;EAAE,WAAW,EAAEf,aAAa,CAAC,MAAM,CAAC,CAACC,QAAQ,GAAG,IAAI;EAAE,cAAc,EAAED,aAAa,CAAC,MAAM,CAAC,CAACM,WAAW,GAAG,IAAI;EAAE,YAAY,EAAEN,aAAa,CAAC,MAAM,CAAC,CAACQ,SAAS,GAAG;AAAK,CAAC,CACrL;AAED,MAAMQ,kBAAkB,GAAID,IAAI,IAAK,CACnC;EAAEE,IAAI,EAAE,WAAW;EAAEC,KAAK,EAAElB,aAAa,CAACe,IAAI,CAAC,CAACN,oBAAoB,CAACC;AAAU,CAAC,EAChF;EAAEO,IAAI,EAAE,OAAO;EAAEC,KAAK,EAAElB,aAAa,CAACe,IAAI,CAAC,CAACN,oBAAoB,CAACE;AAAM,CAAC,CACzE;AAED,MAAMQ,iBAAiB,GAAG,CACtB;EAAEF,IAAI,EAAE,QAAQ;EAAE,kBAAkB,EAAEjB,aAAa,CAAC,MAAM,CAAC,CAACE,iBAAiB;EAAE,aAAa,EAAEF,aAAa,CAAC,MAAM,CAAC,CAACG;AAAc,CAAC,EACnI;EAAEc,IAAI,EAAE,QAAQ;EAAE,kBAAkB,EAAEjB,aAAa,CAAC,MAAM,CAAC,CAACE,iBAAiB;EAAE,aAAa,EAAEF,aAAa,CAAC,MAAM,CAAC,CAACG;AAAc,CAAC,CACtI;AAED,MAAMiB,iBAAiB,GAAG,CACtB;EAAEH,IAAI,EAAE,QAAQ;EAAE,cAAc,EAAEjB,aAAa,CAAC,MAAM,CAAC,CAACM,WAAW;EAAE,eAAe,EAAEN,aAAa,CAAC,MAAM,CAAC,CAACa,YAAY;EAAE,wBAAwB,EAAEb,aAAa,CAAC,MAAM,CAAC,CAACO;AAAqB,CAAC,EAChM;EAAEU,IAAI,EAAE,QAAQ;EAAE,cAAc,EAAEjB,aAAa,CAAC,MAAM,CAAC,CAACM,WAAW;EAAE,eAAe,EAAEN,aAAa,CAAC,MAAM,CAAC,CAACa,YAAY;EAAE,wBAAwB,EAAEb,aAAa,CAAC,MAAM,CAAC,CAACO;AAAqB,CAAC,CACnM;AAED,MAAMc,sBAAsB,GAAG,CAC3B;EAAEJ,IAAI,EAAE,QAAQ;EAAE,iBAAiB,EAAEjB,aAAa,CAAC,MAAM,CAAC,CAACI,iBAAiB;EAAE,aAAa,EAAEJ,aAAa,CAAC,MAAM,CAAC,CAACK;AAAc,CAAC,EAClI;EAAEY,IAAI,EAAE,QAAQ;EAAE,iBAAiB,EAAEjB,aAAa,CAAC,MAAM,CAAC,CAACI,iBAAiB;EAAE,aAAa,EAAEJ,aAAa,CAAC,MAAM,CAAC,CAACK;AAAc,CAAC,CACrI;AAED,MAAMiB,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,CAAC;;AAErC;AACA,MAAMC,aAAa,GAAG,CACpB;EAAEC,QAAQ,EAAE,oEAAoE;EAAEC,OAAO,EAAE,CAAC,WAAW,EAAE,cAAc,EAAE,YAAY,CAAC;EAAEC,aAAa,EAAE,YAAY;EAAEC,IAAI,EAAE;AAA2D,CAAC,EACvO;EAAEH,QAAQ,EAAE,2FAA2F;EAAEC,OAAO,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC;EAAEC,aAAa,EAAE,aAAa;EAAEC,IAAI,EAAE;AAAmF,CAAC,EACxS;EAAEH,QAAQ,EAAE,gFAAgF;EAAEC,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;EAAEC,aAAa,EAAE,OAAO;EAAEC,IAAI,EAAE;AAAkF,CAAC,EAC9P;EAAEH,QAAQ,EAAE,8EAA8E;EAAEC,OAAO,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,mBAAmB,CAAC;EAAEC,aAAa,EAAE,WAAW;EAAEC,IAAI,EAAE;AAAkF,CAAC,EAC3Q;EAAEH,QAAQ,EAAE,sEAAsE;EAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;EAAEC,aAAa,EAAE,MAAM;EAAEC,IAAI,EAAE;AAA+D,CAAC,EACpN;EAAEH,QAAQ,EAAE,0GAA0G;EAAEC,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;EAAEC,aAAa,EAAE,OAAO;EAAEC,IAAI,EAAE;AAAkF,CAAC,EACxR;EAAEH,QAAQ,EAAE,6DAA6D;EAAEC,OAAO,EAAE,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,mBAAmB,CAAC;EAAEC,aAAa,EAAE,mBAAmB;EAAEC,IAAI,EAAE;AAA2C,CAAC,EAC9P;EAAEH,QAAQ,EAAE,+IAA+I;EAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;EAAEC,aAAa,EAAE,MAAM;EAAEC,IAAI,EAAE;AAA2F,CAAC,EACjU;EAAEH,QAAQ,EAAE,gIAAgI;EAAEC,OAAO,EAAE,CAAC,iBAAiB,EAAE,aAAa,CAAC;EAAEC,aAAa,EAAE,iBAAiB;EAAEC,IAAI,EAAE;AAA2C,CAAC,EAC/Q;EAAEH,QAAQ,EAAE,4FAA4F;EAAEC,OAAO,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;EAAEC,aAAa,EAAE,SAAS;EAAEC,IAAI,EAAE;AAA2E,CAAC,CAC9Q;;AAED;AACA,MAAMC,IAAI,GAAGA,CAAC;EAAEC,KAAK;EAAEC,QAAQ;EAAEC;AAAK,CAAC,kBACrChC,OAAA;EAAKiC,SAAS,EAAC,4IAA4I;EAAAF,QAAA,gBACzJ/B,OAAA;IAAKiC,SAAS,EAAC,wBAAwB;IAAAF,QAAA,gBAAC/B,OAAA;MAAKiC,SAAS,EAAC,6BAA6B;MAAAF,QAAA,EAAEC;IAAI;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAAArC,OAAA;MAAIiC,SAAS,EAAC,sCAAsC;MAAAF,QAAA,EAAED;IAAK;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC,eACxKrC,OAAA;IAAKiC,SAAS,EAAC,WAAW;IAAAF,QAAA,EAAEA;EAAQ;IAAAG,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACxC,CACN;AAACC,EAAA,GALIT,IAAI;AAOV,MAAMU,QAAQ,GAAGA,CAAC;EAAET,KAAK;EAAEX,KAAK;EAAEqB,MAAM;EAAER;AAAK,CAAC,kBAC5ChC,OAAA;EAAKiC,SAAS,EAAC,uHAAuH;EAAAF,QAAA,eAAC/B,OAAA;IAAKiC,SAAS,EAAC,mBAAmB;IAAAF,QAAA,gBAAC/B,OAAA;MAAKiC,SAAS,EAAC,6BAA6B;MAAAF,QAAA,EAAEC;IAAI;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAAArC,OAAA;MAAKiC,SAAS,EAAC,MAAM;MAAAF,QAAA,gBAAC/B,OAAA;QAAGiC,SAAS,EAAC,mCAAmC;QAAAF,QAAA,EAAED;MAAK;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAAArC,OAAA;QAAGiC,SAAS,EAAC,kCAAkC;QAAAF,QAAA,EAAEZ;MAAK;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAACG,MAAM,iBAAIxC,OAAA;QAAGiC,SAAS,EAAE,uBAAuBO,MAAM,CAACC,UAAU,CAAC,GAAG,CAAC,GAAG,gBAAgB,GAAG,cAAc,EAAG;QAAAV,QAAA,EAAES;MAAM;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAAK,CAC/f;AAACK,GAAA,GAFIH,QAAQ;AAId,MAAMI,aAAa,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC,KAAK;EAAEC;AAAU,CAAC,KAAK;EAC/D,IAAIH,MAAM,IAAIC,OAAO,IAAIA,OAAO,CAACG,MAAM,EAAE;IACvC,oBAAQhD,OAAA;MAAKiC,SAAS,EAAC,0DAA0D;MAAAF,QAAA,gBAAC/B,OAAA;QAAGiC,SAAS,EAAC,yBAAyB;QAAAF,QAAA,EAAEe;MAAK;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAACQ,OAAO,CAACI,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBAAMnD,OAAA;QAAyBoD,KAAK,EAAE;UAAEC,KAAK,EAAEH,KAAK,CAACG;QAAM,CAAE;QAAAtB,QAAA,EAAE,GAAGmB,KAAK,CAAChC,IAAI,KAAK6B,SAAS,CAACG,KAAK,CAAC/B,KAAK,CAAC;MAAE,GAA3F,QAAQgC,KAAK,EAAE;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAgF,CAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EACvR;EACA,OAAO,IAAI;AACb,CAAC;;AAED;AAAAiB,GAAA,GAPMX,aAAa;AAQnB,eAAe,SAASY,GAAGA,CAAA,EAAG;EAAAC,EAAA;EAC5B,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrF,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACsF,WAAW,EAAEC,cAAc,CAAC,GAAGvF,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClD,MAAM,CAACwF,WAAW,EAAEC,cAAc,CAAC,GAAGzF,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC0F,KAAK,EAAEC,QAAQ,CAAC,GAAG3F,QAAQ,CAAC,CAAC,CAAC;EACrC,MAAM,CAAC4F,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG7F,QAAQ,CAAC,MAAM,CAAC;EACpE,MAAM,CAAC8F,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/F,QAAQ,CAAC,WAAW,CAAC;EAC3E,MAAM,CAACgG,eAAe,EAAEC,kBAAkB,CAAC,GAAGjG,QAAQ,CAAC,CAAC,CAAC;EACzD,MAAM,CAACkG,QAAQ,EAAEC,WAAW,CAAC,GAAGnG,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACoG,eAAe,EAAEC,kBAAkB,CAAC,GAAGrG,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACsG,SAAS,EAAEC,YAAY,CAAC,GAAGvG,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACwG,aAAa,EAAEC,gBAAgB,CAAC,GAAGzG,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC0G,MAAM,EAAEC,SAAS,CAAC,GAAG3G,QAAQ,CAAC,KAAK,CAAC;EAE3CC,SAAS,CAAC,MAAM;IACd,IAAIuF,WAAW,EAAE;MACf,IAAIoB,YAAY,GAAG,CAAC;MACpBzD,aAAa,CAAC0D,OAAO,CAAC,CAACC,CAAC,EAAEhC,KAAK,KAAK;QAClC,IAAIQ,WAAW,CAACR,KAAK,CAAC,KAAKgC,CAAC,CAACxD,aAAa,EAAEsD,YAAY,EAAE;MAC5D,CAAC,CAAC;MACFjB,QAAQ,CAACiB,YAAY,CAAC;;MAEtB;MACA,IAAIA,YAAY,IAAI,CAAC,IAAIN,SAAS,GAAG,CAAC,EAAE;QACtCG,gBAAgB,CAAC,IAAI,CAAC;MACxB;IACF;EACF,CAAC,EAAE,CAACjB,WAAW,EAAEF,WAAW,EAAEgB,SAAS,CAAC,CAAC;EAEzC,MAAMS,kBAAkB,GAAIC,MAAM,IAAKzB,cAAc,CAAC;IAAE,GAAGD,WAAW;IAAE,CAACU,eAAe,GAAGgB;EAAO,CAAC,CAAC;EAEpG,MAAMC,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB1B,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAM2B,SAAS,GAAGA,CAAA,KAAM;IACtB7B,cAAc,CAAC,CAAC,CAAC,CAAC;IAClBE,cAAc,CAAC,KAAK,CAAC;IACrBE,QAAQ,CAAC,CAAC,CAAC;IACXM,kBAAkB,CAAC,CAAC,CAAC;IACrBE,WAAW,CAAC,KAAK,CAAC;IAClBE,kBAAkB,CAAC,KAAK,CAAC;IACzBE,YAAY,CAAC,CAAC,CAAC;IACfE,gBAAgB,CAAC,KAAK,CAAC;IACvBE,SAAS,CAAC,KAAK,CAAC;EAClB,CAAC;EAED,MAAMU,iBAAiB,GAAGA,CAAA,KAAM;IAC9BhB,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMiB,cAAc,GAAGA,CAAA,KAAM;IAC3Bf,YAAY,CAACD,SAAS,GAAG,CAAC,CAAC;IAC3BK,SAAS,CAACD,MAAM,GAAG,IAAI,CAAC;IACxBP,WAAW,CAAC,IAAI,CAAC;IACjBE,kBAAkB,CAAC,KAAK,CAAC;IACzBI,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC;EAC1B,CAAC;EAED,MAAMc,aAAa,GAAGA,CAAA,KAAM;IAC1BlB,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC;EAED,MAAMmB,cAAc,GAAI1E,KAAK,IAAK,IAAI,CAACA,KAAK,GAAG,IAAI,EAAE2E,OAAO,CAAC,CAAC,CAAC,GAAG;EAClE,MAAMC,WAAW,GAAIC,QAAQ,IAAK,IAAI,CAACA,QAAQ,GAAG,OAAO,EAAEF,OAAO,CAAC,CAAC,CAAC,GAAG;EAExE,MAAMG,cAAc,GAAI,CAAChG,aAAa,CAAC,MAAM,CAAC,CAACC,QAAQ,GAAGD,aAAa,CAAC,MAAM,CAAC,CAACC,QAAQ,IAAID,aAAa,CAAC,MAAM,CAAC,CAACC,QAAQ,GAAI,GAAG;EACjI,MAAMgG,iBAAiB,GAAI,CAACjG,aAAa,CAAC,MAAM,CAAC,CAACM,WAAW,GAAGN,aAAa,CAAC,MAAM,CAAC,CAACM,WAAW,IAAIN,aAAa,CAAC,MAAM,CAAC,CAACM,WAAW,GAAI,GAAG;EAC7I,MAAM4F,eAAe,GAAI,CAAClG,aAAa,CAAC,MAAM,CAAC,CAACQ,SAAS,GAAGR,aAAa,CAAC,MAAM,CAAC,CAACQ,SAAS,IAAIR,aAAa,CAAC,MAAM,CAAC,CAACQ,SAAS,GAAI,GAAG;EAErI,MAAM2F,QAAQ,GAAI,CAAC/B,eAAe,GAAG,CAAC,IAAI7C,aAAa,CAACwB,MAAM,GAAI,GAAG;;EAErE;EACA,MAAMqD,mBAAmB,GAAGA,CAAA,kBAC1BrG,OAAA;IAAKiC,SAAS,EAAC,gFAAgF;IAAAF,QAAA,eAC7F/B,OAAA;MAAKiC,SAAS,EAAC,+EAA+E;MAAAF,QAAA,gBAE5F/B,OAAA;QAAKiC,SAAS,EAAC,uEAAuE;QAAAF,QAAA,eACpF/B,OAAA;UAAKiC,SAAS,EAAC,mCAAmC;UAAAF,QAAA,gBAChD/B,OAAA;YAAKiC,SAAS,EAAC,mBAAmB;YAAAF,QAAA,gBAChC/B,OAAA;cAAKiC,SAAS,EAAC,4DAA4D;cAAAF,QAAA,EAAC;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtFrC,OAAA;cAAIiC,SAAS,EAAC,mBAAmB;cAAAF,QAAA,EAAC;YAAqB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACNrC,OAAA;YAAKiC,SAAS,EAAC,yBAAyB;YAAAF,QAAA,EAAC;UAAY;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNrC,OAAA;QAAKiC,SAAS,EAAC,UAAU;QAAAF,QAAA,gBACvB/B,OAAA;UACEsG,GAAG,EAAC,0GAA0G;UAC9GC,GAAG,EAAC,qBAAqB;UACzBtE,SAAS,EAAC;QAA0B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eACFrC,OAAA;UAAKiC,SAAS,EAAC,sFAAsF;UAAAF,QAAA,EAAC;QAEtG;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNrC,OAAA;QAAKiC,SAAS,EAAC,eAAe;QAAAF,QAAA,gBAC5B/B,OAAA;UAAA+B,QAAA,gBACE/B,OAAA;YAAIiC,SAAS,EAAC,sCAAsC;YAAAF,QAAA,EAAC;UAAa;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvErC,OAAA;YAAGiC,SAAS,EAAC,uCAAuC;YAAAF,QAAA,EAAC;UAGrD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENrC,OAAA;UAAKiC,SAAS,EAAC,WAAW;UAAAF,QAAA,gBACxB/B,OAAA;YAAKiC,SAAS,EAAC,kBAAkB;YAAAF,QAAA,gBAC/B/B,OAAA;cAAKiC,SAAS,EAAC,4GAA4G;cAAAF,QAAA,EAAC;YAAC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnIrC,OAAA;cAAA+B,QAAA,gBACE/B,OAAA;gBAAIiC,SAAS,EAAC,qCAAqC;gBAAAF,QAAA,EAAC;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpErC,OAAA;gBAAGiC,SAAS,EAAC,uBAAuB;gBAAAF,QAAA,EAAC;cAAgI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrC,OAAA;YAAKiC,SAAS,EAAC,kBAAkB;YAAAF,QAAA,gBAC/B/B,OAAA;cAAKiC,SAAS,EAAC,4GAA4G;cAAAF,QAAA,EAAC;YAAC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnIrC,OAAA;cAAA+B,QAAA,gBACE/B,OAAA;gBAAIiC,SAAS,EAAC,qCAAqC;gBAAAF,QAAA,EAAC;cAAqB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9ErC,OAAA;gBAAGiC,SAAS,EAAC,uBAAuB;gBAAAF,QAAA,EAAC;cAA6G;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrC,OAAA;YAAKiC,SAAS,EAAC,kBAAkB;YAAAF,QAAA,gBAC/B/B,OAAA;cAAKiC,SAAS,EAAC,4GAA4G;cAAAF,QAAA,EAAC;YAAC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnIrC,OAAA;cAAA+B,QAAA,gBACE/B,OAAA;gBAAIiC,SAAS,EAAC,qCAAqC;gBAAAF,QAAA,EAAC;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtErC,OAAA;gBAAGiC,SAAS,EAAC,uBAAuB;gBAAAF,QAAA,EAAC;cAAqF;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3H,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrC,OAAA;YAAKiC,SAAS,EAAC,kBAAkB;YAAAF,QAAA,gBAC/B/B,OAAA;cAAKiC,SAAS,EAAC,4GAA4G;cAAAF,QAAA,EAAC;YAAC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnIrC,OAAA;cAAA+B,QAAA,gBACE/B,OAAA;gBAAIiC,SAAS,EAAC,qCAAqC;gBAAAF,QAAA,EAAC;cAAmB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5ErC,OAAA;gBAAGiC,SAAS,EAAC,uBAAuB;gBAAAF,QAAA,EAAC;cAA2H;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrC,OAAA;YAAKiC,SAAS,EAAC,kBAAkB;YAAAF,QAAA,gBAC/B/B,OAAA;cAAKiC,SAAS,EAAC,4GAA4G;cAAAF,QAAA,EAAC;YAAC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnIrC,OAAA;cAAA+B,QAAA,gBACE/B,OAAA;gBAAIiC,SAAS,EAAC,qCAAqC;gBAAAF,QAAA,EAAC;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxErC,OAAA;gBAAGiC,SAAS,EAAC,uBAAuB;gBAAAF,QAAA,EAAC;cAAkI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrC,OAAA;UAAKiC,SAAS,EAAC,4DAA4D;UAAAF,QAAA,eACzE/B,OAAA;YAAKiC,SAAS,EAAC,mBAAmB;YAAAF,QAAA,gBAChC/B,OAAA;cAAKiC,SAAS,EAAC,sBAAsB;cAAAF,QAAA,EAAC;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9CrC,OAAA;cAAA+B,QAAA,gBACE/B,OAAA;gBAAIiC,SAAS,EAAC,uCAAuC;gBAAAF,QAAA,EAAC;cAAO;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClErC,OAAA;gBAAGiC,SAAS,EAAC,yBAAyB;gBAAAF,QAAA,EAAC;cAAiH;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNrC,OAAA;UAAKiC,SAAS,EAAC,MAAM;UAAAF,QAAA,eACnB/B,OAAA;YACEwG,OAAO,EAAEA,CAAA,KAAM9C,mBAAmB,CAAC,KAAK,CAAE;YAC1CzB,SAAS,EAAC,kKAAkK;YAAAF,QAAA,EAC7K;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,IAAIoB,gBAAgB,EAAE;IACpB,oBAAOzD,OAAA,CAACqG,mBAAmB;MAAAnE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAChC;EAEA,oBACErC,OAAA;IAAKiC,SAAS,EAAC,iDAAiD;IAAAF,QAAA,gBAC9D/B,OAAA;MAAQiC,SAAS,EAAC,6DAA6D;MAAAF,QAAA,eAC7E/B,OAAA;QAAKiC,SAAS,EAAC,+DAA+D;QAAAF,QAAA,gBAC5E/B,OAAA;UAAKiC,SAAS,EAAC,mBAAmB;UAAAF,QAAA,gBAChC/B,OAAA;YAAIiC,SAAS,EAAC,wBAAwB;YAAAF,QAAA,EAAC;UAAsB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClErC,OAAA;YAAMiC,SAAS,EAAC,uCAAuC;YAAAF,QAAA,EAAC;UAA4B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxF,CAAC,eACNrC,OAAA;UAAKsG,GAAG,EAAC,4GAA4G;UAACC,GAAG,EAAC,gBAAgB;UAACtE,SAAS,EAAC,qBAAqB;UAACmB,KAAK,EAAE;YAACqD,MAAM,EAAE;UAAyB;QAAE;UAAAvE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAETrC,OAAA;MAAMiC,SAAS,EAAC,wCAAwC;MAAAF,QAAA,gBACtD/B,OAAA;QAAKiC,SAAS,EAAC,4CAA4C;QAAAF,QAAA,gBACzD/B,OAAA,CAACuC,QAAQ;UAACT,KAAK,EAAC,kBAAkB;UAACX,KAAK,EAAE,IAAI,CAAClB,aAAa,CAAC,MAAM,CAAC,CAACC,QAAQ,GAAG,OAAO,EAAE4F,OAAO,CAAC,CAAC,CAAC,GAAI;UAACtD,MAAM,EAAE,GAAGyD,cAAc,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,GAAGA,cAAc,CAACH,OAAO,CAAC,CAAC,CAAC,WAAY;UAAC9D,IAAI,eAAEhC,OAAA,CAACX,UAAU;YAAC4C,SAAS,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClPrC,OAAA,CAACuC,QAAQ;UAACT,KAAK,EAAC,qBAAqB;UAACX,KAAK,EAAE,IAAI,CAAClB,aAAa,CAAC,MAAM,CAAC,CAACM,WAAW,GAAG,OAAO,EAAEuF,OAAO,CAAC,CAAC,CAAC,GAAI;UAACtD,MAAM,EAAE,GAAG0D,iBAAiB,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,GAAGA,iBAAiB,CAACJ,OAAO,CAAC,CAAC,CAAC,WAAY;UAAC9D,IAAI,eAAEhC,OAAA,CAACV,UAAU;YAAC2C,SAAS,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9PrC,OAAA,CAACuC,QAAQ;UAACT,KAAK,EAAC,mBAAmB;UAACX,KAAK,EAAE,IAAI,CAAClB,aAAa,CAAC,MAAM,CAAC,CAACQ,SAAS,GAAG,IAAI,EAAEqF,OAAO,CAAC,CAAC,CAAC,GAAI;UAACtD,MAAM,EAAE,GAAG2D,eAAe,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,GAAGA,eAAe,CAACL,OAAO,CAAC,CAAC,CAAC,WAAY;UAAC9D,IAAI,eAAEhC,OAAA,CAACT,WAAW;YAAC0C,SAAS,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjP,CAAC,eAENrC,OAAA;QAAKiC,SAAS,EAAC,4CAA4C;QAAAF,QAAA,gBACvD/B,OAAA;UAAKiC,SAAS,EAAC,eAAe;UAAAF,QAAA,eAC1B/B,OAAA,CAAC6B,IAAI;YAACC,KAAK,EAAC,iCAAiC;YAACE,IAAI,eAAEhC,OAAA,CAACX,UAAU;cAAC4C,SAAS,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAN,QAAA,gBACxF/B,OAAA;cAAKiC,SAAS,EAAC,kBAAkB;cAAAF,QAAA,gBAC7B/B,OAAA;gBAAMiC,SAAS,EAAC,yEAAyE;gBAAAF,QAAA,gBAAC/B,OAAA,CAACL,MAAM;kBAAC+G,IAAI,EAAE,EAAG;kBAACzE,SAAS,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,mBAAe;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EACnJ,CAAC,WAAW,EAAE,cAAc,EAAE,YAAY,CAAC,CAACY,GAAG,CAAC0D,MAAM,iBAAK3G,OAAA;gBAAqBwG,OAAO,EAAEA,CAAA,KAAMpC,sBAAsB,CAACuC,MAAM,CAAE;gBAAC1E,SAAS,EAAE,yDAAyDkC,mBAAmB,KAAKwC,MAAM,GAAG,uBAAuB,GAAG,6CAA6C,EAAG;gBAAA5E,QAAA,EAAE4E;cAAM,GAA9OA,MAAM;gBAAAzE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiP,CAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClU,CAAC,eACNrC,OAAA,CAAClB,mBAAmB;cAAC8H,KAAK,EAAC,MAAM;cAACC,MAAM,EAAE,GAAI;cAAA9E,QAAA,eAAC/B,OAAA,CAACd,SAAS;gBAAC4H,IAAI,EAAE/F,qBAAsB;gBAAAgB,QAAA,gBAAC/B,OAAA,CAACrB,aAAa;kBAACoI,eAAe,EAAC;gBAAK;kBAAA7E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAAArC,OAAA,CAACvB,KAAK;kBAACuI,OAAO,EAAC,MAAM;kBAACC,IAAI,EAAE;oBAAEC,IAAI,EAAE;kBAAU;gBAAE;kBAAAhF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAAArC,OAAA,CAACtB,KAAK;kBAACyI,aAAa,EAAGC,GAAG,IAAK,IAAI,CAACA,GAAG,GAAC,IAAI,EAAEtB,OAAO,CAAC,CAAC,CAAC,GAAI;kBAACmB,IAAI,EAAE;oBAAEC,IAAI,EAAE;kBAAU;gBAAE;kBAAAhF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAAArC,OAAA,CAACpB,OAAO;kBAACyI,OAAO,eAAErH,OAAA,CAAC2C,aAAa;oBAACI,SAAS,EAAGqE,GAAG,IAAK,IAAIA,GAAG,CAACE,cAAc,CAAC,CAAC;kBAAG;oBAAApF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAAArC,OAAA,CAACnB,MAAM;kBAAAqD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAAArC,OAAA,CAACb,IAAI;kBAACoI,IAAI,EAAC,UAAU;kBAACP,OAAO,EAAE7C,mBAAoB;kBAACqD,MAAM,EAAC,SAAS;kBAACN,IAAI,EAAC,SAAS;kBAACO,WAAW,EAAE;gBAAI;kBAAAvF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAqB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNrC,OAAA,CAAC6B,IAAI;UAACC,KAAK,EAAC,+BAA+B;UAACE,IAAI,eAAEhC,OAAA,CAACR,SAAS;YAACyC,SAAS,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAN,QAAA,eACrF/B,OAAA,CAAClB,mBAAmB;YAAC8H,KAAK,EAAC,MAAM;YAACC,MAAM,EAAE,GAAI;YAAA9E,QAAA,eAAC/B,OAAA,CAACzB,QAAQ;cAACuI,IAAI,EAAE,CAACzF,iBAAiB,CAAC,CAAC,CAAC,CAAE;cAAAU,QAAA,gBAAC/B,OAAA,CAACrB,aAAa;gBAACoI,eAAe,EAAC,KAAK;gBAACW,QAAQ,EAAE;cAAM;gBAAAxF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAAArC,OAAA,CAACvB,KAAK;gBAACuI,OAAO,EAAC,MAAM;gBAACC,IAAI,EAAE;kBAAEC,IAAI,EAAE;gBAAU;cAAE;gBAAAhF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAAArC,OAAA,CAACtB,KAAK;gBAACyI,aAAa,EAAEpB,WAAY;gBAACkB,IAAI,EAAE;kBAAEC,IAAI,EAAE;gBAAU;cAAE;gBAAAhF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAAArC,OAAA,CAACpB,OAAO;gBAACyI,OAAO,eAAErH,OAAA,CAAC2C,aAAa;kBAACI,SAAS,EAAE8C;gBAAe;kBAAA3D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAACsF,MAAM,EAAE;kBAACT,IAAI,EAAE;gBAAwB;cAAE;gBAAAhF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAAArC,OAAA,CAACnB,MAAM;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAAArC,OAAA,CAACxB,GAAG;gBAACwI,OAAO,EAAC,cAAc;gBAACE,IAAI,EAAC,SAAS;gBAACU,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;cAAE;gBAAA1F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAAArC,OAAA,CAACxB,GAAG;gBAACwI,OAAO,EAAC,eAAe;gBAACE,IAAI,EAAC,SAAS;gBAACU,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;cAAE;gBAAA1F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAAArC,OAAA,CAACxB,GAAG;gBAACwI,OAAO,EAAC,wBAAwB;gBAACE,IAAI,EAAC,SAAS;gBAACU,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;cAAE;gBAAA1F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAqB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3mB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNrC,OAAA;QAAKiC,SAAS,EAAC,4CAA4C;QAAAF,QAAA,gBACvD/B,OAAA,CAAC6B,IAAI;UAACC,KAAK,EAAC,qBAAqB;UAACE,IAAI,eAAEhC,OAAA,CAACR,SAAS;YAACyC,SAAS,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAN,QAAA,eAC3E/B,OAAA,CAAClB,mBAAmB;YAAC8H,KAAK,EAAC,MAAM;YAACC,MAAM,EAAE,GAAI;YAAA9E,QAAA,eAAC/B,OAAA,CAACzB,QAAQ;cAACuI,IAAI,EAAExF,sBAAuB;cAAAS,QAAA,gBAAC/B,OAAA,CAACrB,aAAa;gBAACoI,eAAe,EAAC,KAAK;gBAACW,QAAQ,EAAE;cAAM;gBAAAxF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAAArC,OAAA,CAACvB,KAAK;gBAACuI,OAAO,EAAC,MAAM;gBAACC,IAAI,EAAE;kBAAEC,IAAI,EAAE;gBAAU;cAAE;gBAAAhF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAAArC,OAAA,CAACtB,KAAK;gBAACyI,aAAa,EAAEpB,WAAY;gBAACkB,IAAI,EAAE;kBAAEC,IAAI,EAAE;gBAAU;cAAE;gBAAAhF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAAArC,OAAA,CAACpB,OAAO;gBAACyI,OAAO,eAAErH,OAAA,CAAC2C,aAAa;kBAACI,SAAS,EAAE8C;gBAAe;kBAAA3D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAACsF,MAAM,EAAE;kBAACT,IAAI,EAAE;gBAAwB;cAAE;gBAAAhF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAAArC,OAAA,CAACnB,MAAM;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAAArC,OAAA,CAACxB,GAAG;gBAACwI,OAAO,EAAC,iBAAiB;gBAACE,IAAI,EAAC,SAAS;gBAACU,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;cAAE;gBAAA1F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAAArC,OAAA,CAACxB,GAAG;gBAACwI,OAAO,EAAC,aAAa;gBAACE,IAAI,EAAC,SAAS;gBAACU,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;cAAE;gBAAA1F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAqB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/hB,CAAC,eACPrC,OAAA,CAAC6B,IAAI;UAACC,KAAK,EAAC,4BAA4B;UAACE,IAAI,eAAEhC,OAAA,CAACN,OAAO;YAACuC,SAAS,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAN,QAAA,eAChF/B,OAAA,CAAClB,mBAAmB;YAAC8H,KAAK,EAAC,MAAM;YAACC,MAAM,EAAE,GAAI;YAAA9E,QAAA,eAAC/B,OAAA,CAACzB,QAAQ;cAACuI,IAAI,EAAE,CAAC1F,iBAAiB,CAAC,CAAC,CAAC,CAAE;cAACyG,MAAM,EAAC,UAAU;cAAA9F,QAAA,gBAAC/B,OAAA,CAACrB,aAAa;gBAACoI,eAAe,EAAC,KAAK;gBAACe,UAAU,EAAE;cAAM;gBAAA5F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eAAArC,OAAA,CAACvB,KAAK;gBAAC8I,IAAI,EAAC,QAAQ;gBAACJ,aAAa,EAAEpB,WAAY;gBAACkB,IAAI,EAAE;kBAAEC,IAAI,EAAE;gBAAU;cAAE;gBAAAhF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eAAArC,OAAA,CAACtB,KAAK;gBAAC6I,IAAI,EAAC,UAAU;gBAACP,OAAO,EAAC,MAAM;gBAACC,IAAI,EAAE;kBAAEC,IAAI,EAAE;gBAAU,CAAE;gBAACN,KAAK,EAAE;cAAG;gBAAA1E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eAAArC,OAAA,CAACpB,OAAO;gBAACyI,OAAO,eAAErH,OAAA,CAAC2C,aAAa;kBAACI,SAAS,EAAE8C;gBAAe;kBAAA3D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAACsF,MAAM,EAAE;kBAACT,IAAI,EAAE;gBAAwB;cAAE;gBAAAhF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eAAArC,OAAA,CAACnB,MAAM;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAAArC,OAAA,CAACxB,GAAG;gBAACwI,OAAO,EAAC,kBAAkB;gBAACe,OAAO,EAAC,GAAG;gBAACb,IAAI,EAAC;cAAS;gBAAAhF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAAArC,OAAA,CAACxB,GAAG;gBAACwI,OAAO,EAAC,aAAa;gBAACe,OAAO,EAAC,GAAG;gBAACb,IAAI,EAAC,SAAS;gBAACU,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;cAAE;gBAAA1F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAqB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1lB,CAAC,eACPrC,OAAA,CAAC6B,IAAI;UAACC,KAAK,EAAC,yBAAyB;UAACE,IAAI,eAAEhC,OAAA,CAACP,OAAO;YAACwC,SAAS,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAN,QAAA,gBAC7E/B,OAAA;YAAKiC,SAAS,EAAC,kBAAkB;YAAAF,QAAA,gBAAC/B,OAAA;cAAMiC,SAAS,EAAC,yEAAyE;cAAAF,QAAA,gBAAC/B,OAAA,CAACL,MAAM;gBAAC+G,IAAI,EAAE,EAAG;gBAACzE,SAAS,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,oBAAgB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAACY,GAAG,CAACjC,IAAI,iBAAKhB,OAAA;cAAmBwG,OAAO,EAAEA,CAAA,KAAMtC,qBAAqB,CAAClD,IAAI,CAAE;cAACiB,SAAS,EAAE,yDAAyDgC,kBAAkB,KAAKjD,IAAI,GAAG,uBAAuB,GAAG,6CAA6C,EAAG;cAAAe,QAAA,EAAEf;YAAI,GAApOA,IAAI;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAyO,CAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1drC,OAAA,CAAClB,mBAAmB;YAAC8H,KAAK,EAAC,MAAM;YAACC,MAAM,EAAE,GAAI;YAAA9E,QAAA,eAAC/B,OAAA,CAACjB,QAAQ;cAAAgD,QAAA,gBAAC/B,OAAA,CAAChB,GAAG;gBAAC8H,IAAI,EAAE7F,kBAAkB,CAACgD,kBAAkB,CAAE;gBAAC+D,EAAE,EAAC,KAAK;gBAACC,EAAE,EAAC,KAAK;gBAACC,SAAS,EAAE,KAAM;gBAACpF,KAAK,EAAEA,CAAC;kBAAE5B,IAAI;kBAAEiH;gBAAQ,CAAC,KAAK,GAAGjH,IAAI,IAAI,CAACiH,OAAO,GAAG,GAAG,EAAErC,OAAO,CAAC,CAAC,CAAC,GAAI;gBAACsC,WAAW,EAAE,GAAI;gBAAClB,IAAI,EAAC,SAAS;gBAACF,OAAO,EAAC,OAAO;gBAAAjF,QAAA,EAAEd,kBAAkB,CAACgD,kBAAkB,CAAC,CAAChB,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBAAMnD,OAAA,CAACf,IAAI;kBAAuBiI,IAAI,EAAE3F,MAAM,CAAC4B,KAAK,GAAG5B,MAAM,CAACyB,MAAM;gBAAE,GAArD,QAAQG,KAAK,EAAE;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAwC,CAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAAArC,OAAA,CAACpB,OAAO;gBAACyI,OAAO,EAAEA,CAAC;kBAAEzE,MAAM;kBAAEC;gBAAQ,CAAC,KAAK;kBAAC,IAAID,MAAM,IAAIC,OAAO,IAAIA,OAAO,CAACG,MAAM,EAAE;oBAAC,MAAM8D,IAAI,GAAGjE,OAAO,CAAC,CAAC,CAAC,CAACA,OAAO;oBAAE,MAAMhC,KAAK,GAAGI,kBAAkB,CAACgD,kBAAkB,CAAC,CAACoE,MAAM,CAAC,CAACC,CAAC,EAAE/C,CAAC,KAAK+C,CAAC,GAAG/C,CAAC,CAACpE,KAAK,EAAE,CAAC,CAAC;oBAAE,oBAAQnB,OAAA;sBAAKiC,SAAS,EAAC,0CAA0C;sBAAAF,QAAA,gBAAC/B,OAAA;wBAAGiC,SAAS,EAAC,WAAW;wBAAAF,QAAA,EAAE+E,IAAI,CAAC5F;sBAAI;wBAAAgB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAAArC,OAAA;wBAAGoD,KAAK,EAAE;0BAAEC,KAAK,EAAER,OAAO,CAAC,CAAC,CAAC,CAACqE;wBAAK,CAAE;wBAAAnF,QAAA,GAAC,UAAQ,EAAC+E,IAAI,CAAC3F,KAAK,CAACmG,cAAc,CAAC,CAAC,EAAC,QAAM;sBAAA;wBAAApF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAAArC,OAAA;wBAAGiC,SAAS,EAAC,uBAAuB;wBAAAF,QAAA,GAAE,CAAE+E,IAAI,CAAC3F,KAAK,GAAGN,KAAK,GAAI,GAAG,EAAEiF,OAAO,CAAC,CAAC,CAAC,EAAC,YAAU;sBAAA;wBAAA5D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAG;kBAAE,OAAO,IAAI;gBAAC;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAAArC,OAAA,CAACnB,MAAM;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAqB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACl8B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENrC,OAAA;QAAKuI,EAAE,EAAC,cAAc;QAACtG,SAAS,EAAC,iEAAiE;QAAAF,QAAA,gBAChG/B,OAAA;UAAKiC,SAAS,EAAC,wCAAwC;UAAAF,QAAA,gBACrD/B,OAAA;YAAKiC,SAAS,EAAC,mBAAmB;YAAAF,QAAA,gBAChC/B,OAAA,CAACZ,UAAU;cAAC6C,SAAS,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/CrC,OAAA;cAAIiC,SAAS,EAAC,uCAAuC;cAAAF,QAAA,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC,EACJ,CAACwB,WAAW,iBAAI7D,OAAA;YAAKiC,SAAS,EAAC,UAAU;YAAAF,QAAA,eAAC/B,OAAA;cAAQwG,OAAO,EAAEA,CAAA,KAAMhC,WAAW,CAAC,CAACD,QAAQ,CAAE;cAACtC,SAAS,EAAC,oDAAoD;cAAAF,QAAA,eAAC/B,OAAA,CAACF,SAAS;gBAAC4G,IAAI,EAAE;cAAG;gBAAAxE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5L,CAAC,EAEL,CAACwB,WAAW,gBACX7D,OAAA;UAAA+B,QAAA,gBACE/B,OAAA;YAAKiC,SAAS,EAAC,4CAA4C;YAAAF,QAAA,eACzD/B,OAAA;cAAKiC,SAAS,EAAC,+BAA+B;cAACmB,KAAK,EAAE;gBAAEwD,KAAK,EAAE,GAAGR,QAAQ,GAAG;gBAAEoC,UAAU,EAAE;cAAyB;YAAE;cAAAtG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1H,CAAC,eACNrC,OAAA;YAAGiC,SAAS,EAAC,wCAAwC;YAAAF,QAAA,GAAC,WAAS,EAACsC,eAAe,GAAG,CAAC,EAAC,MAAI,EAAC7C,aAAa,CAACwB,MAAM;UAAA;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAEjHkC,QAAQ,iBAAIvE,OAAA;YAAKiC,SAAS,EAAC,kFAAkF;YAACwG,IAAI,EAAC,OAAO;YAAA1G,QAAA,gBAAC/B,OAAA;cAAGiC,SAAS,EAAC,WAAW;cAAAF,QAAA,EAAC;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAAArC,OAAA;cAAA+B,QAAA,EAAIP,aAAa,CAAC6C,eAAe,CAAC,CAACzC;YAAI;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAE/MrC,OAAA;YAAKiC,SAAS,EAAC,gDAAgD;YAAAF,QAAA,gBAC7D/B,OAAA;cAAGiC,SAAS,EAAC,sDAAsD;cAAAF,QAAA,EAAEP,aAAa,CAAC6C,eAAe,CAAC,CAAC5C;YAAQ;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjHrC,OAAA;cAAKiC,SAAS,EAAC,WAAW;cAAAF,QAAA,EACvBP,aAAa,CAAC6C,eAAe,CAAC,CAAC3C,OAAO,CAACuB,GAAG,CAAC,CAACoC,MAAM,EAAEqD,CAAC,kBACpD1I,OAAA;gBAAeiC,SAAS,EAAE,+FAA+F0B,WAAW,CAACU,eAAe,CAAC,KAAKgB,MAAM,GAAG,2BAA2B,GAAG,4BAA4B,EAAG;gBAAAtD,QAAA,gBAC9N/B,OAAA;kBAAOuH,IAAI,EAAC,OAAO;kBAACrG,IAAI,EAAE,YAAYmD,eAAe,EAAG;kBAAClD,KAAK,EAAEkE,MAAO;kBAACsD,OAAO,EAAEhF,WAAW,CAACU,eAAe,CAAC,KAAKgB,MAAO;kBAACuD,QAAQ,EAAEA,CAAA,KAAMxD,kBAAkB,CAACC,MAAM,CAAE;kBAACpD,SAAS,EAAC;gBAAoE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvPrC,OAAA;kBAAMiC,SAAS,EAAC,4BAA4B;kBAAAF,QAAA,EAAEsD;gBAAM;kBAAAnD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAFlDqG,CAAC;gBAAAxG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGN,CACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrC,OAAA;YAAKiC,SAAS,EAAC,wCAAwC;YAAAF,QAAA,gBACrD/B,OAAA;cAAQwG,OAAO,EAAEA,CAAA,KAAM;gBAAClC,kBAAkB,CAACa,CAAC,IAAI0D,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE3D,CAAC,GAAG,CAAC,CAAC,CAAC;gBAAEX,WAAW,CAAC,KAAK,CAAC;cAAC,CAAE;cAACuE,QAAQ,EAAE1E,eAAe,KAAK,CAAE;cAACpC,SAAS,EAAC,kKAAkK;cAAAF,QAAA,gBAAC/B,OAAA,CAACJ,SAAS;gBAAC8G,IAAI,EAAE,EAAG;gBAACzE,SAAS,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,aAAS;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACjWgC,eAAe,GAAG7C,aAAa,CAACwB,MAAM,GAAG,CAAC,gBACzChD,OAAA;cAAQwG,OAAO,EAAEA,CAAA,KAAM;gBAAClC,kBAAkB,CAACa,CAAC,IAAI0D,IAAI,CAACG,GAAG,CAACxH,aAAa,CAACwB,MAAM,GAAG,CAAC,EAAEmC,CAAC,GAAG,CAAC,CAAC,CAAC;gBAAEX,WAAW,CAAC,KAAK,CAAC;cAAC,CAAE;cAACvC,SAAS,EAAC,6GAA6G;cAAAF,QAAA,GAAC,OAAK,eAAA/B,OAAA,CAACH,UAAU;gBAAC6G,IAAI,EAAE,EAAG;gBAACzE,SAAS,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,gBAEhSrC,OAAA;cAAQwG,OAAO,EAAElB,YAAa;cAACrD,SAAS,EAAC,+FAA+F;cAAAF,QAAA,EAAC;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACxJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAENrC,OAAA;UAAKiC,SAAS,EAAC,aAAa;UAAAF,QAAA,gBAC1B/B,OAAA;YAAIiC,SAAS,EAAC,uCAAuC;YAAAF,QAAA,EAAC;UAAmB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9ErC,OAAA;YAAGiC,SAAS,EAAC,4BAA4B;YAAAF,QAAA,EAAC;UAAW;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACzDrC,OAAA;YAAKiC,SAAS,EAAE,2BAA2B8B,KAAK,IAAI,CAAC,GAAG,gBAAgB,GAAG,cAAc,EAAG;YAAAhC,QAAA,GACzFgC,KAAK,EAAC,KAAG,EAACvC,aAAa,CAACwB,MAAM;UAAA;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACNrC,OAAA;YAAKiC,SAAS,EAAE,gDAAgD8B,KAAK,IAAI,CAAC,GAAG,cAAc,GAAG,YAAY,EAAG;YAAAhC,QAAA,EAC1GgC,KAAK,IAAI,CAAC,GAAG,8BAA8B,GAAG;UAA4B;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC,eACNrC,OAAA;YAAKiC,SAAS,EAAC,kBAAkB;YAAAF,QAAA,eAC/B/B,OAAA;cAAQwG,OAAO,EAAEf,SAAU;cAACxD,SAAS,EAAC,gKAAgK;cAAAF,QAAA,EAAC;YAEvM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEPrC,OAAA;MAAQiC,SAAS,EAAC,6CAA6C;MAAAF,QAAA,eAAC/B,OAAA;QAAGiC,SAAS,EAAC,SAAS;QAAAF,QAAA,EAAC;MAAwG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACzM,CAAC;AAEV;AAACmB,EAAA,CAhSuBD,GAAG;AAAA0F,GAAA,GAAH1F,GAAG;AAAA,IAAAjB,EAAA,EAAAI,GAAA,EAAAY,GAAA,EAAA2F,GAAA;AAAAC,YAAA,CAAA5G,EAAA;AAAA4G,YAAA,CAAAxG,GAAA;AAAAwG,YAAA,CAAA5F,GAAA;AAAA4F,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}