import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer, <PERSON><PERSON><PERSON>, <PERSON>, Cell, AreaChart, Area } from 'recharts';
import { HelpCircle, TrendingUp, DollarSign, CheckCircle, Bar<PERSON>hart3, <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>, <PERSON><PERSON>3, <PERSON><PERSON>, <PERSON>Lef<PERSON>, ArrowRight, Lightbulb } from 'lucide-react';

// --- Data extracted from the Annual Report (in thousands) ---
const financialData = {
  '2024': {
    netSales: 6899716,
    bottleCanNetSales: 6333316,
    otherNetSales: 566400,
    sparklingNetSales: 4106073,
    stillNetSales: 2227243,
    grossProfit: 2753179,
    incomeFromOperations: 920350,
    netIncome: 633125,
    bottleCanSalesVolume: { sparkling: 266686, still: 86417, total: 353103 },
    sdeaExpenses: 1832829,
  },
  '2023': {
    netSales: 6653858,
    bottleCanNetSales: 6041772,
    otherNetSales: 612086,
    sparklingNetSales: 3892133,
    stillNetSales: 2149639,
    grossProfit: 2598711,
    incomeFromOperations: 834451,
    netIncome: 408375,
    bottleCanSalesVolume: { sparkling: 263872, still: 91495, total: 355367 },
    sdeaExpenses: 1764260,
  },
  '2022': {
    netSales: 6200957,
    grossProfit: 2277954,
    incomeFromOperations: 641047,
    netIncome: 430158,
  }
};

// --- Chart Data Preparation ---
const historicalPerformance = [
  { year: '2022', 'Net Sales': financialData['2022'].netSales / 1000, 'Gross Profit': financialData['2022'].grossProfit / 1000, 'Net Income': financialData['2022'].netIncome / 1000 },
  { year: '2023', 'Net Sales': financialData['2023'].netSales / 1000, 'Gross Profit': financialData['2023'].grossProfit / 1000, 'Net Income': financialData['2023'].netIncome / 1000 },
  { year: '2024', 'Net Sales': financialData['2024'].netSales / 1000, 'Gross Profit': financialData['2024'].grossProfit / 1000, 'Net Income': financialData['2024'].netIncome / 1000 },
];

const getSalesVolumeData = (year) => [
  { name: 'Sparkling', value: financialData[year].bottleCanSalesVolume.sparkling },
  { name: 'Still', value: financialData[year].bottleCanSalesVolume.still },
];

const netSalesBreakdown = [
    { name: 'FY2023', 'Bottle/Can Sales': financialData['2023'].bottleCanNetSales, 'Other Sales': financialData['2023'].otherNetSales },
    { name: 'FY2024', 'Bottle/Can Sales': financialData['2024'].bottleCanNetSales, 'Other Sales': financialData['2024'].otherNetSales },
];

const profitabilityData = [
    { name: 'FY2023', 'Gross Profit': financialData['2023'].grossProfit, 'SD&A Expenses': financialData['2023'].sdeaExpenses, 'Income from Operations': financialData['2023'].incomeFromOperations },
    { name: 'FY2024', 'Gross Profit': financialData['2024'].grossProfit, 'SD&A Expenses': financialData['2024'].sdeaExpenses, 'Income from Operations': financialData['2024'].incomeFromOperations },
];

const revenueCompositionData = [
    { name: 'FY2023', 'Sparkling Sales': financialData['2023'].sparklingNetSales, 'Still Sales': financialData['2023'].stillNetSales },
    { name: 'FY2024', 'Sparkling Sales': financialData['2024'].sparklingNetSales, 'Still Sales': financialData['2024'].stillNetSales },
];

const COLORS = ['#B91C1C', '#F87171'];

// --- Revised Quiz Questions ---
const quizQuestions = [
  { question: "Which metric saw a higher percentage growth from FY2023 to FY2024?", options: ["Net Sales", "Gross Profit", "Net Income"], correctAnswer: "Net Income", hint: "Check the Key Metrics cards at the top of the dashboard." },
  { question: "In FY2024, 'Still Sales' revenue was approximately what percentage of the 'Gross Profit'?", options: ["Approx. 65%", "Approx. 72%", "Approx. 81%", "Approx. 90%"], correctAnswer: "Approx. 81%", hint: "Compare data from the 'Revenue Composition' and 'Profitability Analysis' charts." },
  { question: "What was the operating margin for FY2024? (Income from Operations / Net Sales)", options: ["10.1%", "11.5%", "13.3%", "15.2%"], correctAnswer: "13.3%", hint: "Use data from the 'Profitability Analysis' chart and the top Key Metrics cards." },
  { question: "How did the sales volume for 'Still' beverages change from FY2023 to FY2024?", options: ["Increased", "Decreased", "Remained the same"], correctAnswer: "Decreased", hint: "Use the filter on the 'Bottle/Can Sales Volume' chart to compare the two years." },
  { question: "Which year had the lowest Gross Profit according to the trend chart?", options: ["2022", "2023", "2024"], correctAnswer: "2022", hint: "Filter the 'Performance Trend' chart to show 'Gross Profit'." },
  { question: "What were the Selling, Delivery & Administrative (SD&A) expenses as a percentage of Net Sales in FY2024?", options: ["21.5%", "26.6%", "29.8%", "33.1%"], correctAnswer: "26.6%", hint: "Use data from the 'Profitability Analysis' chart and the top Key Metrics cards." },
  { question: "How did 'Other Sales' revenue change from FY2023 to FY2024?", options: ["Increased by 7.5%", "Decreased by 7.5%", "Stayed the same", "Increased by 2.3%"], correctAnswer: "Decreased by 7.5%", hint: "Look at the 'Net Sales Breakdown' chart." },
  { question: "What was the ratio of Gross Profit to SD&A Expenses in FY2024? (i.e., for every dollar of SD&A expense, how much gross profit was generated?)", options: ["1.20", "1.35", "1.50", "1.75"], correctAnswer: "1.50", hint: "Divide Gross Profit by SD&A Expenses from the 'Profitability Analysis' chart for FY2024." },
  { question: "Which revenue category, 'Sparkling Sales' or 'Still Sales', saw a larger absolute increase in net sales from FY2023 to FY2024?", options: ["Sparkling Sales", "Still Sales"], correctAnswer: "Sparkling Sales", hint: "Analyze the 'Revenue Composition' chart." },
  { question: "What was the approximate total bottle/can sales volume (in thousands of cases) for FY2023?", options: ["355,367", "353,103", "345,821", "360,112"], correctAnswer: "355,367", hint: "Filter the 'Bottle/Can Sales Volume' chart to FY2023 and sum the values." },
];

// --- Helper Components ---
const Card = ({ title, children, icon }) => (
  <div className="bg-white rounded-xl shadow-lg p-3 md:p-4 flex flex-col transition-all duration-300 hover:shadow-2xl hover:scale-105 border border-gray-100">
    <div className="flex items-center mb-3"><div className="p-2 bg-red-100 rounded-full">{icon}</div><h2 className="text-lg font-bold text-gray-800 ml-3">{title}</h2></div>
    <div className="flex-grow">{children}</div>
  </div>
);

const StatCard = ({ title, value, change, icon }) => (
    <div className="bg-white rounded-xl shadow-lg p-4 transition-all duration-300 hover:shadow-2xl hover:scale-105 border border-gray-100"><div className="flex items-center"><div className="p-3 bg-red-100 rounded-full">{icon}</div><div className="ml-4"><p className="text-sm font-medium text-gray-500">{title}</p><p className="text-2xl font-bold text-gray-900">{value}</p>{change && <p className={`text-sm font-medium ${change.startsWith('+') ? 'text-green-600' : 'text-red-600'}`}>{change}</p>}</div></div></div>
);

const CustomTooltip = ({ active, payload, label, formatter }) => {
  if (active && payload && payload.length) {
    return (<div className="bg-white p-4 rounded-lg shadow-lg border border-gray-200"><p className="font-bold text-gray-800">{label}</p>{payload.map((entry, index) => (<p key={`item-${index}`} style={{ color: entry.color }}>{`${entry.name}: ${formatter(entry.value)}`}</p>))}</div>);
  }
  return null;
};

// --- Main App Component ---
export default function App() {
  const [showInstructions, setShowInstructions] = useState(true);
  const [userAnswers, setUserAnswers] = useState({});
  const [showResults, setShowResults] = useState(false);
  const [score, setScore] = useState(0);
  const [selectedVolumeYear, setSelectedVolumeYear] = useState('2024');
  const [selectedTrendMetric, setSelectedTrendMetric] = useState('Net Sales');
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [showHint, setShowHint] = useState(false);

  useEffect(() => {
    if (showResults) {
      let correctCount = 0;
      quizQuestions.forEach((q, index) => {
        if (userAnswers[index] === q.correctAnswer) correctCount++;
      });
      setScore(correctCount);
    }
  }, [showResults, userAnswers]);

  const handleOptionChange = (option) => setUserAnswers({ ...userAnswers, [currentQuestion]: option });

  const handleSubmit = (e) => {
    e.preventDefault();
    setShowResults(true);
  };

  const resetQuiz = () => {
    setUserAnswers({});
    setShowResults(false);
    setScore(0);
    setCurrentQuestion(0);
  };

  const formatCurrency = (value) => `$${(value / 1000).toFixed(2)}M`;
  const formatYAxis = (tickItem) => `$${(tickItem / 1000000).toFixed(1)}B`;

  const netSalesChange = ((financialData['2024'].netSales - financialData['2023'].netSales) / financialData['2023'].netSales) * 100;
  const grossProfitChange = ((financialData['2024'].grossProfit - financialData['2023'].grossProfit) / financialData['2023'].grossProfit) * 100;
  const netIncomeChange = ((financialData['2024'].netIncome - financialData['2023'].netIncome) / financialData['2023'].netIncome) * 100;

  const progress = ((currentQuestion + 1) / quizQuestions.length) * 100;

  // Instructions Overlay Component
  const InstructionsOverlay = () => (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="bg-gradient-to-r from-red-700 to-red-600 text-white p-6 rounded-t-2xl">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="bg-green-500 text-xs px-2 py-1 rounded-full mr-3 font-bold">LIVE</div>
              <h1 className="text-xl font-bold">THE LEDGER OF LEGENDS</h1>
            </div>
            <div className="text-yellow-300 text-sm">⚡ 30,000 PTS</div>
          </div>
        </div>

        {/* Hero Image */}
        <div className="relative">
          <img
            src="https://images.unsplash.com/photo-1554224155-6726b3ff858f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
            alt="Financial Dashboard"
            className="w-full h-48 object-cover"
          />
          <div className="absolute bottom-4 left-4 bg-black bg-opacity-70 text-white px-3 py-1 rounded text-sm">
            📊 Mission Brief
          </div>
        </div>

        {/* Content */}
        <div className="p-6 space-y-4">
          <div>
            <h2 className="text-lg font-bold text-gray-800 mb-2">Mission Brief</h2>
            <p className="text-gray-600 text-sm leading-relaxed">
              Your team must analyze the dashboard of financial data and correctly answer a series of questions.
              Study the charts, metrics, and trends carefully to succeed in this data challenge.
            </p>
          </div>

          <div className="space-y-3">
            <div className="flex items-start">
              <div className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">1</div>
              <div>
                <h3 className="font-semibold text-gray-800 text-sm">How to Play</h3>
                <p className="text-gray-600 text-xs">Explore the charts. You will be presented with a dashboard showing key year's sales data for the Coca-Cola Consolidated company.</p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">2</div>
              <div>
                <h3 className="font-semibold text-gray-800 text-sm">Analyze the Dashboard</h3>
                <p className="text-gray-600 text-xs">Based on the data, you must answer 10 questions about sales trends, profitability, and financial performance.</p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">3</div>
              <div>
                <h3 className="font-semibold text-gray-800 text-sm">Pass with 70%</h3>
                <p className="text-gray-600 text-xs">To pass this challenge, you must answer at least 7 out of the 10 questions correctly.</p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">4</div>
              <div>
                <h3 className="font-semibold text-gray-800 text-sm">Hints are Available</h3>
                <p className="text-gray-600 text-xs">If you come into that HINT, you can reveal the data and see the graphs. Use them wisely to understand the financial trends.</p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">5</div>
              <div>
                <h3 className="font-semibold text-gray-800 text-sm">Time to Analyze</h3>
                <p className="text-gray-600 text-xs">Take your time examining the charts and understanding the data. The quiz will guide you through the data and questions one by one.</p>
              </div>
            </div>
          </div>

          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded-r-lg">
            <div className="flex items-center">
              <div className="text-yellow-600 mr-2">💡</div>
              <div>
                <h4 className="font-semibold text-yellow-800 text-sm">Pro Tip</h4>
                <p className="text-yellow-700 text-xs">Study each chart carefully and look for trends, percentages, and year-over-year changes before starting the quiz.</p>
              </div>
            </div>
          </div>

          {/* Start Button */}
          <div className="pt-4">
            <button
              onClick={() => setShowInstructions(false)}
              className="w-full bg-green-600 hover:bg-green-700 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
            >
              START TASK
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  if (showInstructions) {
    return <InstructionsOverlay />;
  }

  return (
    <div className="bg-gray-50 min-h-screen font-sans text-gray-700">
      <header className="bg-red-700 text-white py-2 px-3 shadow-md sticky top-0 z-10">
        <div className="container mx-auto max-w-7xl flex justify-between items-center">
          <div className="flex items-center">
            <h1 className="text-lg font-bold mr-4">Coca-Cola Consolidated</h1>
            <span className="text-xs text-red-200 hidden md:inline">FY2024 Performance Dashboard</span>
          </div>
          <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/2/24/Coca-Cola_logo.svg/2560px-Coca-Cola_logo.svg.png" alt="Coca-Cola Logo" className="h-6 hidden sm:block" style={{filter: 'brightness(0) invert(1)'}}/>
        </div>
      </header>

      <main className="container mx-auto max-w-7xl p-3 md:p-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <StatCard title="Net Sales (FY24)" value={`$${(financialData['2024'].netSales / 1000000).toFixed(2)}B`} change={`${netSalesChange > 0 ? '+' : ''}${netSalesChange.toFixed(1)}% vs FY23`} icon={<TrendingUp className="h-8 w-8 text-red-600" />} />
          <StatCard title="Gross Profit (FY24)" value={`$${(financialData['2024'].grossProfit / 1000000).toFixed(2)}B`} change={`${grossProfitChange > 0 ? '+' : ''}${grossProfitChange.toFixed(1)}% vs FY23`} icon={<DollarSign className="h-8 w-8 text-red-600" />} />
          <StatCard title="Net Income (FY24)" value={`$${(financialData['2024'].netIncome / 1000).toFixed(2)}M`} change={`${netIncomeChange > 0 ? '+' : ''}${netIncomeChange.toFixed(1)}% vs FY23`} icon={<CheckCircle className="h-8 w-8 text-red-600" />} />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-4">
            <div className="lg:col-span-2">
                <Card title="Performance Trend (in Millions)" icon={<TrendingUp className="text-red-600" />}>
                    <div className="text-center mb-4">
                        <span className="text-sm font-medium text-gray-500 mr-2 flex items-center justify-center"><Filter size={16} className="mr-1"/> Filter Metric:</span>
                        {['Net Sales', 'Gross Profit', 'Net Income'].map(metric => (<button key={metric} onClick={() => setSelectedTrendMetric(metric)} className={`px-3 py-1 text-sm rounded-full mr-2 transition-colors ${selectedTrendMetric === metric ? 'bg-red-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}>{metric}</button>))}
                    </div>
                    <ResponsiveContainer width="100%" height={250}><AreaChart data={historicalPerformance}><CartesianGrid strokeDasharray="3 3" /><XAxis dataKey="year" tick={{ fill: '#4B5563' }} /><YAxis tickFormatter={(val) => `$${(val/1000).toFixed(1)}B`} tick={{ fill: '#4B5563' }} /><Tooltip content={<CustomTooltip formatter={(val) => `$${val.toLocaleString()}`} />} /><Legend /><Area type="monotone" dataKey={selectedTrendMetric} stroke="#B91C1C" fill="#B91C1C" fillOpacity={0.7} /></AreaChart></ResponsiveContainer>
                </Card>
            </div>
            <Card title="Profitability Analysis (FY24)" icon={<BarChart3 className="text-red-600" />}>
                <ResponsiveContainer width="100%" height={250}><BarChart data={[profitabilityData[1]]}><CartesianGrid strokeDasharray="3 3" vertical={false} /><XAxis dataKey="name" tick={{ fill: '#4B5563' }} /><YAxis tickFormatter={formatYAxis} tick={{ fill: '#4B5563' }} /><Tooltip content={<CustomTooltip formatter={formatCurrency} />} cursor={{fill: 'rgba(239, 68, 68, 0.1)'}} /><Legend /><Bar dataKey="Gross Profit" fill="#B91C1C" radius={[4, 4, 0, 0]} /><Bar dataKey="SD&A Expenses" fill="#F87171" radius={[4, 4, 0, 0]} /><Bar dataKey="Income from Operations" fill="#FECACA" radius={[4, 4, 0, 0]} /></BarChart></ResponsiveContainer>
            </Card>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-4">
            <Card title="Revenue Composition" icon={<BarChart3 className="text-red-600" />}>
                <ResponsiveContainer width="100%" height={250}><BarChart data={revenueCompositionData}><CartesianGrid strokeDasharray="3 3" vertical={false} /><XAxis dataKey="name" tick={{ fill: '#4B5563' }} /><YAxis tickFormatter={formatYAxis} tick={{ fill: '#4B5563' }} /><Tooltip content={<CustomTooltip formatter={formatCurrency} />} cursor={{fill: 'rgba(239, 68, 68, 0.1)'}} /><Legend /><Bar dataKey="Sparkling Sales" fill="#B91C1C" radius={[4, 4, 0, 0]} /><Bar dataKey="Still Sales" fill="#F87171" radius={[4, 4, 0, 0]} /></BarChart></ResponsiveContainer>
            </Card>
            <Card title="Net Sales Breakdown (FY24)" icon={<Layers3 className="text-red-600" />}>
                <ResponsiveContainer width="100%" height={250}><BarChart data={[netSalesBreakdown[1]]} layout="vertical"><CartesianGrid strokeDasharray="3 3" horizontal={false}/><XAxis type="number" tickFormatter={formatYAxis} tick={{ fill: '#4B5563' }}/><YAxis type="category" dataKey="name" tick={{ fill: '#4B5563' }} width={60}/><Tooltip content={<CustomTooltip formatter={formatCurrency} />} cursor={{fill: 'rgba(239, 68, 68, 0.1)'}}/><Legend /><Bar dataKey="Bottle/Can Sales" stackId="a" fill="#B91C1C" /><Bar dataKey="Other Sales" stackId="a" fill="#F87171" radius={[0, 4, 4, 0]}/></BarChart></ResponsiveContainer>
            </Card>
            <Card title="Bottle/Can Sales Volume" icon={<PieIcon className="text-red-600" />}>
                <div className="text-center mb-2"><span className="text-sm font-medium text-gray-500 mr-2 flex items-center justify-center"><Filter size={16} className="mr-1"/> Filter by Year:</span>{['2024', '2023'].map(year => (<button key={year} onClick={() => setSelectedVolumeYear(year)} className={`px-3 py-1 text-sm rounded-full mr-2 transition-colors ${selectedVolumeYear === year ? 'bg-red-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}>{year}</button>))}</div>
                <ResponsiveContainer width="100%" height={220}><PieChart><Pie data={getSalesVolumeData(selectedVolumeYear)} cx="50%" cy="50%" labelLine={false} label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`} outerRadius={100} fill="#8884d8" dataKey="value">{getSalesVolumeData(selectedVolumeYear).map((entry, index) => (<Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />))}</Pie><Tooltip content={({ active, payload }) => {if (active && payload && payload.length) {const data = payload[0].payload; const total = getSalesVolumeData(selectedVolumeYear).reduce((s, e) => s + e.value, 0); return (<div className="bg-white p-3 rounded-lg shadow-lg border"><p className="font-bold">{data.name}</p><p style={{ color: payload[0].fill }}>Volume: {data.value.toLocaleString()} cases</p><p className="text-sm text-gray-600">{((data.value / total) * 100).toFixed(1)}% of Total</p></div>); } return null;}} /><Legend /></PieChart></ResponsiveContainer>
            </Card>
        </div>

        <div id="quiz-section" className="bg-white rounded-xl shadow-lg p-4 md:p-5 border border-gray-100">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <HelpCircle className="text-red-600 h-8 w-8" />
              <h2 className="text-2xl font-bold text-gray-800 ml-3">Data Challenge</h2>
            </div>
             {!showResults && <div className="relative"><button onClick={() => setShowHint(!showHint)} className="text-gray-500 hover:text-red-600 transition-colors"><Lightbulb size={24}/></button></div>}
          </div>

          {!showResults ? (
            <div>
              <div className="w-full bg-gray-200 rounded-full h-2.5 mb-4">
                <div className="bg-red-600 h-2.5 rounded-full" style={{ width: `${progress}%`, transition: 'width 0.5s ease-in-out' }}></div>
              </div>
              <p className="text-center text-sm text-gray-500 mb-4">Question {currentQuestion + 1} of {quizQuestions.length}</p>

              {showHint && <div className="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-4 rounded-r-lg" role="alert"><p className="font-bold">Hint</p><p>{quizQuestions[currentQuestion].hint}</p></div>}

              <div className="p-3 min-h-[180px] flex flex-col justify-center">
                <p className="font-semibold text-xl text-gray-800 mb-4 text-center">{quizQuestions[currentQuestion].question}</p>
                <div className="space-y-3">
                  {quizQuestions[currentQuestion].options.map((option, i) => (
                    <label key={i} className={`flex items-center p-4 rounded-lg hover:bg-red-100 cursor-pointer transition-colors border-2 ${userAnswers[currentQuestion] === option ? 'bg-red-100 border-red-500' : 'bg-gray-50 border-gray-200'}`}>
                      <input type="radio" name={`question-${currentQuestion}`} value={option} checked={userAnswers[currentQuestion] === option} onChange={() => handleOptionChange(option)} className="form-radio h-5 w-5 text-red-600 focus:ring-red-500 border-gray-300" />
                      <span className="ml-4 text-gray-700 text-lg">{option}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div className="flex justify-between items-center mt-6">
                <button onClick={() => {setCurrentQuestion(q => Math.max(0, q - 1)); setShowHint(false);}} disabled={currentQuestion === 0} className="bg-gray-300 text-gray-700 font-bold py-2 px-6 rounded-full hover:bg-gray-400 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"><ArrowLeft size={16} className="mr-1"/> Previous</button>
                {currentQuestion < quizQuestions.length - 1 ? (
                  <button onClick={() => {setCurrentQuestion(q => Math.min(quizQuestions.length - 1, q + 1)); setShowHint(false);}} className="bg-red-600 text-white font-bold py-2 px-6 rounded-full hover:bg-red-700 transition-colors flex items-center">Next <ArrowRight size={16} className="ml-1"/></button>
                ) : (
                  <button onClick={handleSubmit} className="bg-green-600 text-white font-bold py-2 px-6 rounded-full hover:bg-green-700 transition-colors">Submit</button>
                )}
              </div>
            </div>
          ) : (
            <div className="text-center">
              <h3 className="text-3xl font-bold text-gray-800 mb-2">Challenge Complete!</h3>
              <p className="text-lg text-gray-600 mb-4">You scored:</p>
              <div className={`text-6xl font-bold mb-4 ${score >= 7 ? 'text-green-500' : 'text-red-500'}`}>
                {score} / {quizQuestions.length}
              </div>
              <div className={`p-4 rounded-lg text-white font-bold text-2xl ${score >= 7 ? 'bg-green-500' : 'bg-red-500'}`}>
                {score >= 7 ? 'Congratulations, You Passed!' : 'Keep Analyzing, Try Again!'}
              </div>
              <div className="text-center mt-8">
                <button onClick={resetQuiz} className="bg-gray-600 text-white font-bold py-3 px-8 rounded-full hover:bg-gray-700 transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-1">
                  Try Again
                </button>
              </div>
            </div>
          )}
        </div>
      </main>

      <footer className="bg-gray-800 text-white text-center p-3 mt-4"><p className="text-sm">Dashboard created based on the Coca-Cola Consolidated 2024 Annual Report. For educational purposes only.</p></footer>
    </div>
  );
}
