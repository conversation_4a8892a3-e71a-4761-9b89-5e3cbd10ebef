body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Utility classes for the dashboard */
.bg-gray-50 { background-color: #f9fafb; }
.bg-white { background-color: #ffffff; }
.bg-red-700 { background-color: #b91c1c; }
.bg-red-600 { background-color: #dc2626; }
.bg-red-100 { background-color: #fee2e2; }
.bg-gray-200 { background-color: #e5e7eb; }
.bg-gray-300 { background-color: #d1d5db; }
.bg-gray-600 { background-color: #4b5563; }
.bg-gray-700 { background-color: #374151; }
.bg-gray-800 { background-color: #1f2937; }
.bg-green-500 { background-color: #10b981; }
.bg-green-600 { background-color: #059669; }
.bg-yellow-100 { background-color: #fef3c7; }

.text-white { color: #ffffff; }
.text-gray-700 { color: #374151; }
.text-gray-800 { color: #1f2937; }
.text-gray-900 { color: #111827; }
.text-gray-500 { color: #6b7280; }
.text-gray-600 { color: #4b5563; }
.text-red-600 { color: #dc2626; }
.text-red-200 { color: #fecaca; }
.text-green-500 { color: #10b981; }
.text-green-600 { color: #059669; }
.text-yellow-700 { color: #a16207; }

.min-h-screen { min-height: 100vh; }
.font-sans { font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif; }
.font-bold { font-weight: 700; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }

.text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-6xl { font-size: 3.75rem; line-height: 1; }

.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.px-8 { padding-left: 2rem; padding-right: 2rem; }
.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }

.m-0 { margin: 0; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-8 { margin-bottom: 2rem; }
.ml-3 { margin-left: 0.75rem; }
.ml-4 { margin-left: 1rem; }
.mr-1 { margin-right: 0.25rem; }
.mr-2 { margin-right: 0.5rem; }
.mt-6 { margin-top: 1.5rem; }
.mt-8 { margin-top: 2rem; }

.rounded-xl { border-radius: 0.75rem; }
.rounded-full { border-radius: 9999px; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-r-lg { border-top-right-radius: 0.5rem; border-bottom-right-radius: 0.5rem; }

.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
.shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
.shadow-2xl { box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); }

.border { border-width: 1px; }
.border-2 { border-width: 2px; }
.border-l-4 { border-left-width: 4px; }
.border-gray-100 { border-color: #f3f4f6; }
.border-gray-200 { border-color: #e5e7eb; }
.border-gray-300 { border-color: #d1d5db; }
.border-red-500 { border-color: #ef4444; }
.border-yellow-500 { border-color: #eab308; }

.flex { display: flex; }
.grid { display: grid; }
.hidden { display: none; }
.block { display: block; }

.flex-col { flex-direction: column; }
.flex-grow { flex-grow: 1; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.text-center { text-align: center; }

.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.gap-6 { gap: 1.5rem; }
.gap-8 { gap: 2rem; }
.gap-3 { gap: 0.75rem; }

.sticky { position: sticky; }
.relative { position: relative; }
.top-0 { top: 0; }
.z-10 { z-index: 10; }

.container { width: 100%; }
.mx-auto { margin-left: auto; margin-right: auto; }
.max-w-7xl { max-width: 80rem; }

.h-16 { height: 4rem; }
.h-8 { height: 2rem; }
.h-5 { height: 1.25rem; }
.h-2\.5 { height: 0.625rem; }
.w-8 { width: 2rem; }
.w-5 { width: 1.25rem; }
.w-full { width: 100%; }
.w-60 { width: 15rem; }

.space-y-3 > * + * { margin-top: 0.75rem; }

.transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.duration-300 { transition-duration: 300ms; }

.hover\:shadow-2xl:hover { box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); }
.hover\:scale-105:hover { transform: scale(1.05); }
.hover\:bg-gray-300:hover { background-color: #d1d5db; }
.hover\:bg-gray-400:hover { background-color: #9ca3af; }
.hover\:bg-gray-700:hover { background-color: #374151; }
.hover\:bg-red-700:hover { background-color: #b91c1c; }
.hover\:bg-green-700:hover { background-color: #047857; }
.hover\:bg-red-100:hover { background-color: #fee2e2; }
.hover\:text-red-600:hover { color: #dc2626; }
.hover\:shadow-lg:hover { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
.hover\:-translate-y-1:hover { transform: translateY(-0.25rem); }

.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }

.disabled\:opacity-50:disabled { opacity: 0.5; }
.disabled\:cursor-not-allowed:disabled { cursor: not-allowed; }

.min-h-\[250px\] { min-height: 250px; }
.min-h-\[180px\] { min-height: 180px; }

.h-10 { height: 2.5rem; }
.h-6 { height: 1.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.p-5 { padding: 1.25rem; }
.gap-4 { gap: 1rem; }
.mb-4 { margin-bottom: 1rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.mr-4 { margin-right: 1rem; }

.hidden { display: none; }
.inline { display: inline; }

/* Overlay and modal styles */
.fixed { position: fixed; }
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.bg-opacity-75 { background-color: rgba(0, 0, 0, 0.75); }
.z-50 { z-index: 50; }
.max-w-2xl { max-width: 42rem; }
.max-h-\[90vh\] { max-height: 90vh; }
.overflow-y-auto { overflow-y: auto; }
.rounded-2xl { border-radius: 1rem; }
.rounded-t-2xl { border-top-left-radius: 1rem; border-top-right-radius: 1rem; }
.rounded-r-lg { border-top-right-radius: 0.5rem; border-bottom-right-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }

/* Gradient backgrounds */
.bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)); }
.from-red-700 { --tw-gradient-from: #b91c1c; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(185, 28, 28, 0)); }
.to-red-600 { --tw-gradient-to: #dc2626; }

/* Additional colors */
.bg-green-500 { background-color: #10b981; }
.bg-blue-500 { background-color: #3b82f6; }
.bg-yellow-50 { background-color: #fffbeb; }
.text-yellow-300 { color: #fcd34d; }
.text-yellow-600 { color: #d97706; }
.text-yellow-700 { color: #a16207; }
.text-yellow-800 { color: #92400e; }
.border-yellow-400 { border-color: #fbbf24; }

/* Spacing */
.space-y-3 > * + * { margin-top: 0.75rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.mt-0\.5 { margin-top: 0.125rem; }
.pt-4 { padding-top: 1rem; }

/* Sizing */
.w-6 { width: 1.5rem; }
.h-6 { height: 1.5rem; }
.h-48 { height: 12rem; }

/* Object fit */
.object-cover { object-fit: cover; }

/* Transform and scale */
.transform { transform: translateX(var(--tw-translate-x)) translateY(var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.hover\:scale-105:hover { --tw-scale-x: 1.05; --tw-scale-y: 1.05; }

/* Leading */
.leading-relaxed { line-height: 1.625; }

@media (min-width: 768px) {
  .md\:inline { display: inline; }
}

.form-radio { appearance: none; background-color: #fff; border: 1px solid #d1d5db; border-radius: 50%; width: 1.25rem; height: 1.25rem; }
.form-radio:checked { background-color: #dc2626; border-color: #dc2626; }

@media (min-width: 768px) {
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .md\:p-6 { padding: 1.5rem; }
  .md\:p-8 { padding: 2rem; }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .lg\:col-span-2 { grid-column: span 2 / span 2; }
}

@media (min-width: 640px) {
  .sm\:block { display: block; }
}
